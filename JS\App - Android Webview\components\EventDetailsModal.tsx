import React from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  Modal, 
  TouchableOpacity, 
  ScrollView,
  SafeAreaView,
  Platform
} from 'react-native';
import { X, Copy, ExternalLink } from 'lucide-react-native';
import { formatDate } from '@/utils/dateFormatter';
import Colors from '@/constants/Colors';

interface EventDetailsModalProps {
  isVisible: boolean;
  event: any;
  onClose: () => void;
}

export default function EventDetailsModal({ isVisible, event, onClose }: EventDetailsModalProps) {
  if (!event) return null;

  const copyToClipboard = () => {
    // In a real app, this would use Clipboard.setString
    alert('Event data copied to clipboard');
  };

  const openInNewTab = () => {
    // In a real app, this would open the URL in a new tab/browser
    alert('Would open in new tab: ' + event.url);
  };

  const formatJson = (json: any) => {
    return JSON.stringify(json, null, 2);
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <View style={[
              styles.eventTypeBadge, 
              event.type === 'dataLayerEvent' ? styles.dataLayerBadge : styles.ga4Badge
            ]}>
              <Text style={styles.eventTypeBadgeText}>
                {event.type === 'dataLayerEvent' ? 'Data Layer Event' : 'GA4 Event'}
              </Text>
            </View>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.eventMeta}>
            <Text style={styles.timestampLabel}>Captured at:</Text>
            <Text style={styles.timestamp}>{formatDate(event.timestamp, true)}</Text>
          </View>
          
          <View style={styles.urlContainer}>
            <Text style={styles.urlLabel}>URL:</Text>
            <Text style={styles.url} numberOfLines={2}>{event.url}</Text>
            <TouchableOpacity style={styles.openUrlButton} onPress={openInNewTab}>
              <ExternalLink size={16} color={Colors.light.tint} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.dataHeader}>
            <Text style={styles.dataTitle}>Event Data</Text>
            <TouchableOpacity style={styles.copyButton} onPress={copyToClipboard}>
              <Copy size={16} color={Colors.light.tint} />
              <Text style={styles.copyText}>Copy</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.jsonContainer}>
            <Text style={styles.jsonText}>{formatJson(event.data)}</Text>
          </ScrollView>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: Platform.OS === 'ios' ? 0 : 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  eventTypeBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
  },
  dataLayerBadge: {
    backgroundColor: '#0A84FF20',
  },
  ga4Badge: {
    backgroundColor: '#FF950020',
  },
  eventTypeBadgeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0A84FF',
  },
  closeButton: {
    padding: 4,
  },
  eventMeta: {
    marginBottom: 12,
  },
  timestampLabel: {
    fontSize: 12,
    color: '#666',
  },
  timestamp: {
    fontSize: 14,
    color: '#333',
  },
  urlContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  urlLabel: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  url: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  openUrlButton: {
    padding: 6,
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 12,
  },
  dataHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dataTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f2f2f2',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
  },
  copyText: {
    fontSize: 12,
    color: Colors.light.tint,
    fontWeight: '500',
    marginLeft: 4,
  },
  jsonContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    maxHeight: 400,
  },
  jsonText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: '#333',
  },
});