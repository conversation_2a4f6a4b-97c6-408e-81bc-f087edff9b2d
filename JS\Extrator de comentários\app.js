console.log('Ol<PERSON> mundo!')

// === CONFIGURAÇÃO ===
// Insira suas chaves de API abaixo:
const YOUTUBE_API_KEY = 'AIzaSyB4gonOW_WWIDQOBAuxKWExHyvWgjOWsyA'; // Substitua pela sua chave
const GEMINI_API_KEY = 'AIzaSyDxAmC_d5I6dSlFyeX_vB0dNMApCZia4uI'; // Substitua pela sua chave

// Função para extrair o ID do vídeo a partir da URL
function extrairVideoId(url) {
    // Aceita URLs como https://www.youtube.com/watch?v=ID ou https://youtu.be/ID
    const regex = /(?:v=|\.be\/)([\w-]{11})/;
    const match = url.match(regex);
    return match ? match[1] : url.trim(); // Se não for URL, assume que é o ID
}

// Função para buscar comentários do YouTube
async function buscarComentarios() {
    const urlInput = document.getElementById('videoUrl').value.trim();
    const videoId = extrairVideoId(urlInput);
    if (!videoId) {
        alert('Informe a URL ou o ID do vídeo!');
        return;
    }
    const url = `https://www.googleapis.com/youtube/v3/commentThreads?part=snippet&videoId=${videoId}&key=${YOUTUBE_API_KEY}&maxResults=10`;
    document.getElementById('comentarios').innerHTML = 'Carregando...';
    try {
        const resp = await fetch(url);
        const data = await resp.json();
        if (!data.items) throw new Error('Nenhum comentário encontrado.');
        mostrarComentarios(data.items);
    } catch (e) {
        document.getElementById('comentarios').innerHTML = 'Erro ao buscar comentários.';
    }
}

// Exibe comentários e botão para interpretar
function mostrarComentarios(comentarios) {
    const div = document.getElementById('comentarios');
    div.innerHTML = '';
    comentarios.forEach((item, idx) => {
        const texto = item.snippet.topLevelComment.snippet.textDisplay;
        div.innerHTML += `<div class='comment' id='c${idx}'>${texto}<br><button onclick='interpretarComentario(${JSON.stringify(texto)}, ${idx})'>Interpretar</button><div class='interpretation' id='i${idx}'></div></div>`;
    });
}

// Função para enviar comentário ao Gemini
async function interpretarComentario(texto, idx) {
    document.getElementById('i'+idx).innerText = 'Interpretando...';
    try {
        const resp = await fetch('https://generativelanguage.googleapis.com/v1beta/models/chat-bison-001:generateContent?key='+GEMINI_API_KEY, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                contents: [{ parts: [{ text: `Interprete o seguinte comentário do YouTube: ${texto}` }] }]
            })
        });
        const data = await resp.json();
        // Para depuração: exiba a resposta completa no console
        console.log('Gemini response:', data);
        let interpretacao = 'Sem resposta.';
        if (data.candidates && data.candidates[0]) {
            // Gemini pode retornar em diferentes formatos, tente extrair o texto
            if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0].text) {
                interpretacao = data.candidates[0].content.parts[0].text;
            } else if (data.candidates[0].content && data.candidates[0].content.text) {
                interpretacao = data.candidates[0].content.text;
            } else if (data.candidates[0].output) {
                interpretacao = data.candidates[0].output;
            }
        } else if (data.promptFeedback && data.promptFeedback.blockReason) {
            interpretacao = 'Comentário bloqueado pela API: ' + data.promptFeedback.blockReason;
        }
        document.getElementById('i'+idx).innerText = interpretacao;
    } catch (e) {
        document.getElementById('i'+idx).innerText = 'Erro ao interpretar.';
    }
}