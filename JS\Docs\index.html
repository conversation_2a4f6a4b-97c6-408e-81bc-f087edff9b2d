<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GA4 Data Sources & AI Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .slide {
            width: 90%;
            max-width: 1200px;
            height: 90%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 60px;
            display: none;
            flex-direction: column;
            justify-content: center;
            position: relative;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.8s ease-out;
        }

        .slide.active {
            display: flex;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #fff, #ffd700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            line-height: 1.2;
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: #ffd700;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 500;
            margin: 1.5rem 0 1rem 0;
            color: #87ceeb;
        }

        .slide p, .slide li {
            font-size: 1.3rem;
            line-height: 1.6;
            margin-bottom: 1rem;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .slide ul {
            list-style: none;
            padding-left: 0;
        }

        .slide li {
            padding-left: 2rem;
            position: relative;
            margin-bottom: 0.8rem;
        }

        .slide li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: #ffd700;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .data-source-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .data-source-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .data-source-card h4 {
            font-size: 1.5rem;
            color: #ffd700;
            margin-bottom: 1rem;
            text-align: center;
        }

        .rating {
            display: flex;
            justify-content: center;
            margin: 1rem 0;
        }

        .star {
            color: #ffd700;
            font-size: 1.5rem;
            margin: 0 0.1rem;
        }

        .star.empty {
            color: rgba(255, 215, 0, 0.3);
        }

        .navigation {
            position: absolute;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 1rem;
            z-index: 100;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .architecture-diagram {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            line-height: 1.8;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .highlight {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
            font-weight: bold;
        }

        .ai-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .ai-feature {
            background: linear-gradient(45deg, rgba(135, 206, 235, 0.2), rgba(255, 215, 0, 0.1));
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .ai-feature:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .ai-feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }

        .roadmap {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 2rem 0;
            gap: 1rem;
        }

        .roadmap-phase {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid rgba(255, 215, 0, 0.3);
            position: relative;
        }

        .roadmap-phase::after {
            content: "→";
            position: absolute;
            right: -2rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            color: #ffd700;
        }

        .roadmap-phase:last-child::after {
            display: none;
        }

        .phase-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 0.5rem;
        }

        .phase-duration {
            font-size: 0.9rem;
            color: #87ceeb;
            margin-bottom: 1rem;
        }

        .thank-you {
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        .thank-you h1 {
            font-size: 4rem;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slide-counter">
            <span id="currentSlide">1</span> / <span id="totalSlides">10</span>
        </div>

        <!-- Slide 1: Título -->
        <div class="slide active">
            <h1>Comparação de Fontes de Dados GA4</h1>
            <h2>+ Integração com Inteligência Artificial</h2>
            <div style="text-align: center; margin-top: 3rem;">
                <p style="font-size: 1.5rem; color: #87ceeb;">Maximizando o Potencial dos Seus Dados de Analytics</p>
                <p style="font-size: 1.2rem; margin-top: 2rem;">Supersonic Analytics • 2025</p>
            </div>
        </div>

        <!-- Slide 2: Agenda -->
        <div class="slide">
            <h2>Agenda</h2>
            <ul style="font-size: 1.4rem; line-height: 2;">
                <li>Visão geral das fontes de dados GA4</li>
                <li>Comparação detalhada: API, BigQuery, Looker Studio, AWS</li>
                <li>Diferenças na qualidade e precisão dos dados</li>
                <li>Integração com Inteligência Artificial</li>
                <li>Casos de uso por indústria</li>
                <li>Arquiteturas recomendadas</li>
                <li>Roadmap de implementação</li>
                <li>Recomendações finais</li>
            </ul>
        </div>

        <!-- Slide 3: Hierarquia das Fontes -->
        <div class="slide">
            <h2>Hierarquia de Precisão dos Dados</h2>
            <div class="comparison-grid">
                <div class="data-source-card">
                    <h4>🏆 AWS/Cloud Storage</h4>
                    <div class="rating">
                        <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span>
                    </div>
                    <p>Máxima precisão e controle</p>
                </div>
                <div class="data-source-card">
                    <h4>🥈 BigQuery</h4>
                    <div class="rating">
                        <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star empty">★</span>
                    </div>
                    <p>Dados brutos, 100% precisos</p>
                </div>
                <div class="data-source-card">
                    <h4>🥉 GA4 API</h4>
                    <div class="rating">
                        <span class="star">★</span><span class="star">★</span><span class="star">★</span><span class="star empty">★</span><span class="star empty">★</span>
                    </div>
                    <p>Dados processados consistentes</p>
                </div>
                <div class="data-source-card">
                    <h4>📊 Looker Studio</h4>
                    <div class="rating">
                        <span class="star">★</span><span class="star">★</span><span class="star empty">★</span><span class="star empty">★</span><span class="star empty">★</span>
                    </div>
                    <p>Facilidade vs. Precisão</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: GA4 API -->
        <div class="slide">
            <h2>GA4 API</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem;">
                <div>
                    <h3>✅ Vantagens</h3>
                    <ul>
                        <li>Dados processados e "prontos"</li>
                        <li>Consistência com interface GA4</li>
                        <li>Gratuito até certos limites</li>
                        <li>Integrações automatizadas</li>
                    </ul>
                </div>
                <div>
                    <h3>⚠️ Limitações</h3>
                    <ul>
                        <li>Amostragem em alto volume</li>
                        <li>Limitações de dimensões/métricas</li>
                        <li>Retenção limitada (14 meses)</li>
                        <li>Quotas de requisições</li>
                    </ul>
                </div>
            </div>
            <div class="highlight" style="margin-top: 2rem; text-align: center; font-size: 1.4rem;">
                <strong>Ideal para:</strong> Dashboards corporativos e integrações que precisam de consistência com GA4
            </div>
        </div>

        <!-- Slide 5: BigQuery -->
        <div class="slide">
            <h2>BigQuery</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem;">
                <div>
                    <h3>✅ Vantagens</h3>
                    <ul>
                        <li>Dados brutos não amostrados</li>
                        <li>Granularidade evento por evento</li>
                        <li>Exportação GA4 gratuita</li>
                        <li>Flexibilidade total para SQL</li>
                        <li>Integração nativa com ML</li>
                    </ul>
                </div>
                <div>
                    <h3>⚠️ Desafios</h3>
                    <ul>
                        <li>Requer conhecimento SQL avançado</li>
                        <li>Necessidade de processar dados brutos</li>
                        <li>Custos por consulta/armazenamento</li>
                        <li>Complexidade de implementação</li>
                    </ul>
                </div>
            </div>
            <div class="highlight" style="margin-top: 2rem; text-align: center; font-size: 1.4rem;">
                <strong>Ideal para:</strong> Análises avançadas, Data Science e criação de métricas customizadas
            </div>
        </div>

        <!-- Slide 6: AWS/Cloud -->
        <div class="slide">
            <h2>AWS/Cloud Data Warehouse</h2>
            <div class="ai-features">
                <div class="ai-feature">
                    <span class="ai-feature-icon">🏛️</span>
                    <h4>Controle Total</h4>
                    <p>Propriedade e governança completa dos dados</p>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">🔗</span>
                    <h4>Integração</h4>
                    <p>Múltiplas fontes: GA4, CRM, ERP, APIs</p>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">🚀</span>
                    <h4>Escalabilidade</h4>
                    <p>Auto-scaling e processamento distribuído</p>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">🤖</span>
                    <h4>IA Avançada</h4>
                    <p>Machine Learning e IA generativa</p>
                </div>
            </div>
            <div class="architecture-diagram">
                GA4 → BigQuery → AWS S3 → Redshift/Athena → Ferramentas de BI<br>
                                    ↓<br>
                            SageMaker (ML) → Lambda → APIs
            </div>
        </div>

        <!-- Slide 7: Capacidades de IA -->
        <div class="slide">
            <h2>Integração com Inteligência Artificial</h2>
            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin: 2rem 0;">
                <div style="text-align: center; font-weight: bold; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    Capacidade
                </div>
                <div style="text-align: center; font-weight: bold; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    GA4 API
                </div>
                <div style="text-align: center; font-weight: bold; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    BigQuery
                </div>
                <div style="text-align: center; font-weight: bold; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    AWS/Cloud
                </div>
                
                <div style="padding: 0.8rem;">Modelos Simples</div>
                <div style="text-align: center; padding: 0.8rem;">⚠️</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
                
                <div style="padding: 0.8rem;">Deep Learning</div>
                <div style="text-align: center; padding: 0.8rem;">❌</div>
                <div style="text-align: center; padding: 0.8rem;">⚠️</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
                
                <div style="padding: 0.8rem;">Real-time ML</div>
                <div style="text-align: center; padding: 0.8rem;">❌</div>
                <div style="text-align: center; padding: 0.8rem;">⚠️</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
                
                <div style="padding: 0.8rem;">MLOps</div>
                <div style="text-align: center; padding: 0.8rem;">❌</div>
                <div style="text-align: center; padding: 0.8rem;">⚠️</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
                
                <div style="padding: 0.8rem;">IA Generativa</div>
                <div style="text-align: center; padding: 0.8rem;">❌</div>
                <div style="text-align: center; padding: 0.8rem;">⚠️</div>
                <div style="text-align: center; padding: 0.8rem;">✅</div>
            </div>
        </div>

        <!-- Slide 8: Casos de Uso por Indústria -->
        <div class="slide">
            <h2>Casos de Uso por Indústria</h2>
            <div class="ai-features">
                <div class="ai-feature">
                    <span class="ai-feature-icon">🛒</span>
                    <h4>E-commerce</h4>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Recomendação de produtos</li>
                        <li>Otimização de preços</li>
                        <li>Prevenção abandono carrinho</li>
                    </ul>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">💻</span>
                    <h4>SaaS/Tech</h4>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Product analytics</li>
                        <li>Usage prediction</li>
                        <li>Churn prevention</li>
                    </ul>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">📺</span>
                    <h4>Media/Content</h4>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Content recommendation</li>
                        <li>Audience segmentation</li>
                        <li>Ad optimization</li>
                    </ul>
                </div>
                <div class="ai-feature">
                    <span class="ai-feature-icon">🏦</span>
                    <h4>Finance</h4>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Risk assessment</li>
                        <li>Fraud detection</li>
                        <li>Customer scoring</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 9: Roadmap -->
        <div class="slide">
            <h2>Roadmap de Implementação de IA</h2>
            <div class="roadmap">
                <div class="roadmap-phase">
                    <div class="phase-title">Fase 1: Fundação</div>
                    <div class="phase-duration">0-3 meses</div>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Pipeline de dados</li>
                        <li>Feature store</li>
                        <li>Modelos simples</li>
                    </ul>
                </div>
                <div class="roadmap-phase">
                    <div class="phase-title">Fase 2: Evolução</div>
                    <div class="phase-duration">3-6 meses</div>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>ML avançado</li>
                        <li>ML Ops básico</li>
                        <li>A/B testing</li>
                    </ul>
                </div>
                <div class="roadmap-phase">
                    <div class="phase-title">Fase 3: Maturidade</div>
                    <div class="phase-duration">6-12 meses</div>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Real-time ML</li>
                        <li>Feature engineering</li>
                        <li>Ensembles</li>
                    </ul>
                </div>
                <div class="roadmap-phase">
                    <div class="phase-title">Fase 4: Inovação</div>
                    <div class="phase-duration">12+ meses</div>
                    <ul style="text-align: left; font-size: 1rem;">
                        <li>Deep learning</li>
                        <li>IA generativa</li>
                        <li>Computer vision</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 10: Recomendações -->
        <div class="slide thank-you">
            <h1>Recomendações Finais</h1>
            <div class="contact-info">
                <h3>Escolha baseada em:</h3>
                <ul style="font-size: 1.3rem; margin: 2rem 0; text-align: left;">
                    <li><span class="highlight">Precisão necessária:</span> AWS/Cloud ≥ BigQuery > GA4 API > Looker Studio</li>
                    <li><span class="highlight">Facilidade de uso:</span> Looker Studio > GA4 API > BigQuery > AWS/Cloud</li>
                    <li><span class="highlight">Capacidades de IA:</span> AWS/Cloud > BigQuery > GA4 API > Looker Studio</li>
                    <li><span class="highlight">Controle e governança:</span> AWS/Cloud > BigQuery > GA4 API > Looker Studio</li>
                </ul>
                <p style="font-size: 1.2rem; text-align: center; margin-top: 2rem; color: #87ceeb;">
                    <strong>Para projetos com IA/ML:</strong> Priorize fontes com dados granulares e não amostrados
                </p>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← Anterior</button>
            <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Próximo →</button>
        </div>
    </div>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('totalSlides').textContent = totalSlides;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            slides[index].classList.add('active');
            
            document.getElementById('currentSlide').textContent = index + 1;
            
            // Update navigation buttons
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === totalSlides - 1;
        }

        function changeSlide(direction) {
            const newIndex = currentSlideIndex + direction;
            if (newIndex >= 0 && newIndex < totalSlides) {
                currentSlideIndex = newIndex;
                showSlide(currentSlideIndex);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                changeSlide(1);
            } else if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>