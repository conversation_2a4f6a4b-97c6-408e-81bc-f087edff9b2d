import React, { useState } from 'react';
import { StyleSheet, Text, View, Switch, TouchableOpacity, TextInput, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSettingsStore } from '@/store/settingsStore';
import { useEventStore } from '@/store/eventStore';
import { useHistoryStore } from '@/store/historyStore';
import { DatabaseBackup, Smartphone, Shield, Moon, Sun, Info, RefreshCw } from 'lucide-react-native';
import Colors from '@/constants/Colors';

export default function SettingsScreen() {
  const { 
    darkMode, 
    userAgent, 
    autoCapture, 
    captureDataLayer, 
    captureGA4, 
    updateDarkMode, 
    updateUserAgent,
    updateAutoCapture,
    updateCaptureDataLayer,
    updateCaptureGA4
  } = useSettingsStore();
  
  const { clearEvents } = useEventStore();
  const { clearHistory } = useHistoryStore();
  
  const [editingUserAgent, setEditingUserAgent] = useState(false);
  const [tempUserAgent, setTempUserAgent] = useState(userAgent);

  const handleSaveUserAgent = () => {
    updateUserAgent(tempUserAgent);
    setEditingUserAgent(false);
  };

  const handleClearAllData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all captured events and browsing history. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: () => {
            clearEvents();
            clearHistory();
            Alert.alert('Success', 'All data has been cleared.');
          }
        }
      ]
    );
  };

  const renderSettingSwitch = (title, description, value, onValueChange) => (
    <View style={styles.settingItem}>
      <View style={styles.settingTextContainer}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#e0e0e0', true: Colors.light.tint + '80' }}
        thumbColor={value ? Colors.light.tint : '#f4f3f4'}
        ios_backgroundColor="#e0e0e0"
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['right', 'left']}>
      <ScrollView>
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Shield size={20} color={Colors.light.tint} />
            <Text style={styles.sectionTitle}>Capture Settings</Text>
          </View>

          {renderSettingSwitch(
            'Auto Capture Events',
            'Automatically capture events when browsing',
            autoCapture,
            updateAutoCapture
          )}

          {renderSettingSwitch(
            'Capture Data Layer Events',
            'Monitor and capture dataLayer events',
            captureDataLayer,
            updateCaptureDataLayer
          )}

          {renderSettingSwitch(
            'Capture GA4 Events',
            'Monitor and capture GA4 events',
            captureGA4,
            updateCaptureGA4
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Smartphone size={20} color={Colors.light.tint} />
            <Text style={styles.sectionTitle}>Browser Settings</Text>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingTitle}>User Agent</Text>
              <Text style={styles.settingDescription}>Custom user agent string for the browser</Text>
            </View>
          </View>

          {editingUserAgent ? (
            <View style={styles.userAgentEditor}>
              <TextInput
                style={styles.userAgentInput}
                value={tempUserAgent}
                onChangeText={setTempUserAgent}
                multiline
                numberOfLines={3}
              />
              <View style={styles.userAgentActions}>
                <TouchableOpacity 
                  style={[styles.userAgentButton, styles.userAgentCancelButton]} 
                  onPress={() => {
                    setTempUserAgent(userAgent);
                    setEditingUserAgent(false);
                  }}
                >
                  <Text style={styles.userAgentCancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.userAgentButton, styles.userAgentSaveButton]} 
                  onPress={handleSaveUserAgent}
                >
                  <Text style={styles.userAgentSaveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View style={styles.userAgentDisplay}>
              <Text style={styles.userAgentText} numberOfLines={2}>
                {userAgent || 'Default User Agent'}
              </Text>
              <TouchableOpacity 
                style={styles.editButton} 
                onPress={() => setEditingUserAgent(true)}
              >
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
          )}

          {renderSettingSwitch(
            'Dark Mode',
            'Use dark color theme',
            darkMode,
            updateDarkMode
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <DatabaseBackup size={20} color={Colors.light.tint} />
            <Text style={styles.sectionTitle}>Data Management</Text>
          </View>

          <TouchableOpacity style={styles.dataActionButton} onPress={handleClearAllData}>
            <Text style={styles.dataActionButtonText}>Clear All Data</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Info size={20} color={Colors.light.tint} />
            <Text style={styles.sectionTitle}>About</Text>
          </View>

          <View style={styles.aboutContainer}>
            <Text style={styles.appName}>WebView Event Monitor</Text>
            <Text style={styles.appVersion}>Version 1.0.0</Text>
            <Text style={styles.appDescription}>
              Monitor Data Layer and GA4 events in websites using React Native WebView
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },
  settingTextContainer: {
    flex: 1,
    paddingRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingDescription: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  userAgentDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },
  userAgentText: {
    flex: 1,
    fontSize: 13,
    color: '#666',
    fontFamily: 'monospace',
  },
  editButton: {
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  editButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  userAgentEditor: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f2f2f2',
  },
  userAgentInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 10,
    fontSize: 13,
    fontFamily: 'monospace',
    backgroundColor: '#f9f9f9',
    height: 80,
    textAlignVertical: 'top',
  },
  userAgentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  userAgentButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 8,
  },
  userAgentCancelButton: {
    backgroundColor: '#f2f2f2',
  },
  userAgentCancelButtonText: {
    color: '#666',
  },
  userAgentSaveButton: {
    backgroundColor: Colors.light.tint,
  },
  userAgentSaveButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  dataActionButton: {
    backgroundColor: '#FF453A',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  dataActionButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  aboutContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  appName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
    lineHeight: 20,
  },
});