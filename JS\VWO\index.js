const scraper = require('./scraper');
const { formatResults } = require('./utils');

async function main() {
  try {
    console.log('Starting VWO experiment data extraction...');
    
    const targetUrl = 'https://facamedicina.afya.com.br/';
    const results = await scraper.extractVwoData(targetUrl);
    
    if (results) {
      formatResults(results);
    } else {
      console.log('No VWO experiment data was found.');
    }
  } catch (error) {
    console.error('Error occurred during execution:', error.message);
    process.exit(1);
  }
}

main();