// Script otimizado para capturar TODOS os eventos GA4 incluindo Enhanced Measurement
const injectTrackingScript = `
(function(window) {
  'use strict';

  // Previne múltiplas injeções do script
  if (window.__eventMonitorInjected) return;
  window.__eventMonitorInjected = true;

  // Configurações
  const CONFIG = {
    DEBUG: true, // Ativado para debug - altere para false em produção
    THROTTLE_INTERVAL: 100, // ms
    MAX_EVENT_CACHE: 1000,
    PERIODIC_CHECK_INTERVAL: 500, // ms - reduzido para detectar mais rápido
    PERIODIC_CHECK_TIMEOUT: 20000, // ms
    NETWORK_TIMEOUT: 5000, // ms
    GA4_ENDPOINTS: [
      'google-analytics.com/g/collect',
      'google-analytics.com/mp/collect',
      'googletagmanager.com/g/collect',
      'analytics.google.com/g/collect'
    ]
  };

  // Utilitários melhorados
  const Utils = {
    throttle(fn, wait) {
      let last = 0;
      return function(...args) {
        const now = Date.now();
        if (now - last > wait) {
          last = now;
          fn.apply(this, args);
        }
      };
    },

    debounce(fn, wait) {
      let timeout;
      return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => fn.apply(this, args), wait);
      };
    },

    log(level, message, data) {
      if (!CONFIG.DEBUG) return;
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
      const prefix = \`[GA4Monitor:\${level}] \${timestamp}\`;
      
      if (data !== undefined) {
        console[level === 'error' ? 'error' : 'log'](prefix + ' ' + message, data);
      } else {
        console[level === 'error' ? 'error' : 'log'](prefix + ' ' + message);
      }
    },

    hashEvent(event) {
      try {
        const normalized = {
          type: event.type,
          event: event.event || event.payload?.event,
          url: event.url ? event.url.split('?')[0] : undefined
        };
        return btoa(JSON.stringify(normalized));
      } catch {
        return \`fallback_\${Date.now()}_\${Math.random().toString(36).substr(2, 5)}\`;
      }
    },

    // Parse de parâmetros da URL do GA4
    parseGA4Params(url) {
      try {
        const urlObj = new URL(url);
        const params = {};
        
        // Parâmetros comuns do GA4
        const paramMap = {
          'en': 'event_name',
          'tid': 'measurement_id', 
          'cid': 'client_id',
          'sid': 'session_id',
          'sct': 'session_count',
          'seg': 'session_engaged',
          'dl': 'page_location',
          'dt': 'page_title',
          'dr': 'page_referrer',
          'ul': 'language',
          'sr': 'screen_resolution',
          'vp': 'viewport_size',
          'de': 'document_encoding',
          'sd': 'screen_colors',
          'epn.page_title': 'page_title_param',
          'epn.page_location': 'page_location_param'
        };

        urlObj.searchParams.forEach((value, key) => {
          const mappedKey = paramMap[key] || key;
          params[mappedKey] = decodeURIComponent(value);
        });

        return params;
      } catch (error) {
        Utils.log('error', 'Error parsing GA4 URL params', error);
        return {};
      }
    },

    // Extrai evento do payload ou URL
    extractEventName(data) {
      if (data.event) return data.event;
      if (data.payload && data.payload.event) return data.payload.event;
      if (data.params && data.params.event_name) return data.params.event_name;
      if (data.en) return data.en;
      return 'unknown_event';
    }
  };

  // Cache de eventos
  const eventCache = new Set();
  const EventCache = {
    add(hash) {
      if (eventCache.size >= CONFIG.MAX_EVENT_CACHE) {
        const firstItem = eventCache.values().next().value;
        eventCache.delete(firstItem);
      }
      eventCache.add(hash);
      return true;
    },
    has(hash) {
      return eventCache.has(hash);
    }
  };

  // Comunicação com React Native
  const MessageHandler = {
    send: Utils.throttle((message) => {
      try {
        if (window.ReactNativeWebView && typeof window.ReactNativeWebView.postMessage === 'function') {
          window.ReactNativeWebView.postMessage(message);
          const parsed = JSON.parse(message);
          Utils.log('info', \`📤 Sent to RN: \${parsed.type}\`, parsed.payload);
        } else {
          Utils.log('error', '❌ ReactNativeWebView not available');
        }
      } catch (error) {
        Utils.log('error', '❌ Failed to send message', error);
      }
    }, CONFIG.THROTTLE_INTERVAL),

    sendEvent(type, payload, additionalData = {}) {
      const eventData = {
        type,
        payload,
        timestamp: Date.now(),
        url: window.location.href,
        ...additionalData
      };
      
      const hash = Utils.hashEvent(eventData);
      if (!EventCache.has(hash)) {
        EventCache.add(hash);
        this.send(JSON.stringify(eventData));
        return true;
      } else {
        Utils.log('debug', \`🔄 Event duplicate, skipping: \${type}\`);
        return false;
      }
    }
  };

  // Monitor do DataLayer APRIMORADO
  const DataLayerMonitor = {
    isMonitored: false,
    originalPush: null,

    setup() {
      if (this.isMonitored) return true;
      
      if (!window.dataLayer) {
        Utils.log('info', '⏳ DataLayer not found yet');
        return false;
      }

      Utils.log('info', '🔧 Setting up DataLayer monitoring');

      try {
        this.originalPush = window.dataLayer.push;
        window.dataLayer.push = (...args) => {
          // Chama o push original primeiro
          const result = this.originalPush.apply(window.dataLayer, args);
          try {
            args.forEach(eventData => {
              if (eventData && typeof eventData === 'object') {
                Utils.log('info', '📊 DataLayer push detected', eventData);
                // Sempre envia como dataLayerEvent
                MessageHandler.sendEvent('dataLayerEvent', eventData);
                // NÃO tenta mais enviar como ga4Event aqui!
              }
            });
          } catch (error) {
            Utils.log('error', '❌ Error processing dataLayer push', error);
          }
          return result;
        };

        // Processa eventos já existentes no dataLayer
        if (window.dataLayer.length > 0) {
          Utils.log('info', \`📋 Processing \${window.dataLayer.length} existing dataLayer items\`);
          window.dataLayer.forEach((item, index) => {
            if (item && typeof item === 'object' && item.event) {
              Utils.log('info', \`📊 Existing dataLayer event \${index}: \${item.event}\`, item);
              MessageHandler.sendEvent('ga4Event', {
                event: item.event,
                params: item
              }, { source: 'existing_datalayer' });
            }
          });
        }

        this.isMonitored = true;
        window.dataLayer._monitored = true;
        Utils.log('info', '✅ DataLayer monitoring active');
        return true;
      } catch (error) {
        Utils.log('error', '❌ Failed to setup DataLayer monitoring', error);
        return false;
      }
    }
  };

  // Monitor do gtag MELHORADO
  const GtagMonitor = {
    originalGtag: null,
    isMonitored: false,

    setup() {
      if (this.isMonitored) return;

      Utils.log('info', '🔧 Setting up gtag monitoring');

      // Cria dataLayer se não existir
      window.dataLayer = window.dataLayer || [];

      // Salva gtag original ou cria um novo
      this.originalGtag = window.gtag || function() {
        window.dataLayer.push(arguments);
      };

      // Override do gtag
      window.gtag = (...args) => {
        const [command, ...restArgs] = args;
        
        // Chama função original primeiro
        const result = this.originalGtag.apply(this, args);

        try {
          Utils.log('info', \`🏷️ gtag call: \${command}\`, restArgs);

          if (command === 'event' && restArgs.length > 0) {
            const eventName = restArgs[0];
            const eventParams = restArgs[1] || {};

            Utils.log('info', \`🎯 GA4 event via gtag: \${eventName}\`, eventParams);
            MessageHandler.sendEvent('ga4Event', {
              event: eventName,
              params: eventParams
            }, { source: 'gtag' });

          } else if (command === 'config' && restArgs.length > 0) {
            const measurementId = restArgs[0];
            const configParams = restArgs[1] || {};

            Utils.log('info', \`⚙️ GA4 config: \${measurementId}\`, configParams);
            MessageHandler.sendEvent('ga4Config', {
              measurementId,
              configParams
            });
          }
        } catch (error) {
          Utils.log('error', '❌ Error processing gtag call', error);
        }

        return result;
      };

      this.isMonitored = true;
      Utils.log('info', '✅ gtag monitoring active');
    }
  };

  // Monitor de Rede SUPER MELHORADO para capturar Enhanced Measurement
  const NetworkMonitor = {
    isActive: false,

    setup() {
      if (this.isActive) return;
      Utils.log('info', '🌐 Setting up network monitoring');

      try {
        this.interceptFetch();
        this.interceptXHR();
        this.interceptBeacon(); // Adiciona interceptação de sendBeacon
        this.isActive = true;
        Utils.log('info', '✅ Network monitoring active');
      } catch (error) {
        Utils.log('error', '❌ Failed to setup network monitoring', error);
      }
    },

    isGA4Request(url) {
      if (!url || typeof url !== 'string') return false;
      return CONFIG.GA4_ENDPOINTS.some(endpoint => url.includes(endpoint));
    },

    processGA4Request(url, method, body) {
      try {
        const params = Utils.parseGA4Params(url);
        const eventName = params.event_name || params.en || 'page_view';
        
        // Adiciona dados do body se disponível (especialmente para POST)
        let enhancedParams = { ...params };
        if (body && typeof body === 'string') {
          try {
            // Tenta fazer parse do body como form data
            const bodyParams = new URLSearchParams(body);
            bodyParams.forEach((value, key) => {
              if (!enhancedParams[key]) {
                enhancedParams[key] = decodeURIComponent(value);
              }
            });
          } catch (e) {
            // Se não conseguir fazer parse, adiciona como raw
            enhancedParams._raw_body = body.substring(0, 200); // Limita tamanho
          }
        }

        Utils.log('info', \`🌐 GA4 \${method} request: \${eventName}\`, { 
          url: url.substring(0, 100) + '...', 
          params: enhancedParams 
        });

        MessageHandler.sendEvent('ga4NetworkEvent', {
          event: eventName,
          params: enhancedParams,
          method,
          timestamp: Date.now()
        });

        // Se for page_view, envia evento especial
        if (eventName === 'page_view') {
          Utils.log('info', '📄 PAGE_VIEW detected via network!', enhancedParams);
          MessageHandler.sendEvent('ga4PageView', {
            page_location: enhancedParams.page_location || enhancedParams.dl || window.location.href,
            page_title: enhancedParams.page_title || enhancedParams.dt || document.title,
            page_referrer: enhancedParams.page_referrer || enhancedParams.dr || document.referrer,
            client_id: enhancedParams.client_id || enhancedParams.cid,
            session_id: enhancedParams.session_id || enhancedParams.sid
          });
        }

      } catch (error) {
        Utils.log('error', '❌ Error processing GA4 request', error);
      }
    },

    interceptFetch() {
      const originalFetch = window.fetch;
      window.fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;
        
        if (NetworkMonitor.isGA4Request(url)) {
          const body = init && init.body ? init.body : null;
          NetworkMonitor.processGA4Request(url, 'fetch', body);
        }

        return originalFetch.apply(this, arguments);
      };
    },

    interceptXHR() {
      const originalOpen = XMLHttpRequest.prototype.open;
      const originalSend = XMLHttpRequest.prototype.send;

      XMLHttpRequest.prototype.open = function(method, url, ...rest) {
        this._ga4Monitor = { method, url };
        return originalOpen.apply(this, [method, url, ...rest]);
      };

      XMLHttpRequest.prototype.send = function(body) {
        if (this._ga4Monitor && NetworkMonitor.isGA4Request(this._ga4Monitor.url)) {
          NetworkMonitor.processGA4Request(this._ga4Monitor.url, 'xhr', body);
        }
        return originalSend.apply(this, arguments);
      };
    },

    // Intercepta sendBeacon (usado frequentemente pelo GA4)
    interceptBeacon() {
      if (!navigator.sendBeacon) return;

      const originalSendBeacon = navigator.sendBeacon;
      navigator.sendBeacon = function(url, data) {
        if (NetworkMonitor.isGA4Request(url)) {
          NetworkMonitor.processGA4Request(url, 'beacon', data);
        }
        return originalSendBeacon.apply(this, arguments);
      };
    }
  };

  // Detector de Scripts GA4 MELHORADO
  const ScriptDetector = {
    scan() {
      Utils.log('info', '🔍 Scanning for GA4 scripts');

      // Scripts externos
      document.querySelectorAll('script[src]').forEach(script => {
        const src = script.src;
        if (this.isGA4Script(src)) {
          Utils.log('info', '📜 GA4 script found', src);
          MessageHandler.sendEvent('ga4ScriptDetected', { src });
          
          // Extrai measurement ID da URL se possível
          const measurementMatch = src.match(/id=([^&]+)/);
          if (measurementMatch) {
            Utils.log('info', '🆔 Measurement ID from script', measurementMatch[1]);
            MessageHandler.sendEvent('ga4ConfigDetected', {
              measurementId: measurementMatch[1],
              source: 'script_src'
            });
          }
        }
      });

      // Scripts inline com conteúdo GA4
      document.querySelectorAll('script:not([src])').forEach(script => {
        const content = script.textContent || '';
        if (this.hasGA4Content(content)) {
          Utils.log('info', '📜 Inline GA4 script found');
          
          const measurementId = this.extractMeasurementId(content);
          if (measurementId) {
            Utils.log('info', '🆔 Measurement ID from inline script', measurementId);
            MessageHandler.sendEvent('ga4ConfigDetected', {
              measurementId,
              source: 'inline_script'
            });
          }
        }
      });
    },

    isGA4Script(src) {
      return src.includes('googletagmanager.com/gtag/js') ||
             src.includes('google-analytics.com/analytics.js') ||
             src.includes('googletagmanager.com/gtm.js');
    },

    hasGA4Content(content) {
      return content.includes('gtag') ||
             content.includes('google-analytics') ||
             content.includes('GoogleAnalytics') ||
             content.includes('dataLayer') ||
             content.includes('G-');
    },

    extractMeasurementId(content) {
      const patterns = [
        /['"]G-[A-Z0-9]+['"]/g,
        /gtag\\s*\\(\\s*['"]config['"]\\s*,\\s*['"]([^'"]+)['"]/g,
        /GA_MEASUREMENT_ID\\s*[:=]\\s*['"]([^'"]+)['"]/g
      ];

      for (const pattern of patterns) {
        const match = content.match(pattern);
        if (match && match[0]) {
          return match[0].replace(/['"]/g, '');
        }
      }
      return null;
    }
  };

  // Observer do DOM OTIMIZADO
  const DOMObserver = {
    observer: null,

    setup() {
      this.observer = new MutationObserver(Utils.debounce((mutations) => {
        let hasNewScripts = false;

        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeName === 'SCRIPT') {
              hasNewScripts = true;
              Utils.log('info', '📜 New script detected in DOM');
              
              if (node.src) {
                node.addEventListener('load', () => {
                  setTimeout(() => {
                    ScriptDetector.scan();
                    this.reinitializeTracking();
                  }, 500);
                });
              }
            }
          });
        });

        if (hasNewScripts) {
          setTimeout(() => {
            ScriptDetector.scan();
            this.reinitializeTracking();
          }, 200);
        }
      }, 300));

      this.observer.observe(document.documentElement, {
        childList: true,
        subtree: true
      });

      Utils.log('info', '👁️ DOM observer active');
    },

    reinitializeTracking() {
      if (!DataLayerMonitor.isMonitored && window.dataLayer) {
        DataLayerMonitor.setup();
      }
      
      if (!GtagMonitor.isMonitored) {
        GtagMonitor.setup();
      }
    }
  };

  // Inicializador Principal MELHORADO
  const TrackingInitializer = {
    initialized: false,

    init() {
      if (this.initialized) return;

      Utils.log('info', '🚀 Initializing GA4 tracking system');

      try {
        // Detecção imediata
        ScriptDetector.scan();

        // Setup de todos os monitores
        DataLayerMonitor.setup();
        GtagMonitor.setup();
        NetworkMonitor.setup();
        DOMObserver.setup();

        // Verificação periódica para dataLayer
        this.setupPeriodicCheck();

        // Monitora mudanças de página para SPAs
        this.setupPageChangeMonitoring();

        // Notificação inicial
        this.sendInitialNotification();

        this.initialized = true;
        Utils.log('info', '✅ GA4 tracking system initialized successfully');
      } catch (error) {
        Utils.log('error', '❌ Failed to initialize tracking system', error);
      }
    },

    setupPeriodicCheck() {
      const checkInterval = setInterval(() => {
        // Verifica dataLayer
        if (window.dataLayer && !DataLayerMonitor.isMonitored) {
          Utils.log('info', '🔄 DataLayer found in periodic check');
          DataLayerMonitor.setup();
        }
        
        // Verifica gtag
        if (window.gtag && !GtagMonitor.isMonitored) {
          Utils.log('info', '🔄 gtag found in periodic check');
          GtagMonitor.setup();
        }
      }, CONFIG.PERIODIC_CHECK_INTERVAL);

      setTimeout(() => {
        clearInterval(checkInterval);
        Utils.log('info', '⏰ Periodic check timeout reached');
      }, CONFIG.PERIODIC_CHECK_TIMEOUT);
    },

    setupPageChangeMonitoring() {
      // Para SPAs - monitora mudanças de URL
      let currentUrl = window.location.href;
      
      const checkUrlChange = () => {
        if (window.location.href !== currentUrl) {
          const oldUrl = currentUrl;
          currentUrl = window.location.href;
          Utils.log('info', \`🔄 URL change detected: \${oldUrl} → \${currentUrl}\`);
          
          MessageHandler.sendEvent('pageChange', {
            from: oldUrl,
            to: currentUrl,
            timestamp: Date.now()
          });
        }
      };

      // Monitora através de eventos de histórico
      window.addEventListener('popstate', checkUrlChange);
      
      // Monitora através de polling (backup para SPAs)
      setInterval(checkUrlChange, 1000);
    },

    sendInitialNotification() {
      try {
        MessageHandler.sendEvent('trackingScriptInjected', {
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          dataLayerPresent: !!window.dataLayer,
          gtagPresent: typeof window.gtag === 'function',
          dataLayerLength: window.dataLayer ? window.dataLayer.length : 0
        });
        Utils.log('info', '📤 Initial notification sent');
      } catch (error) {
        Utils.log('error', '❌ Failed to send initial notification', error);
      }
    }
  };

  // Setup de event listeners
  const setupEventListeners = () => {
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      Utils.log('info', '📄 Document ready, initializing immediately');
      setTimeout(TrackingInitializer.init, 100); // Pequeno delay para garantir que tudo está pronto
    } else {
      Utils.log('info', '⏳ Document not ready, setting up event listeners');
      
      window.addEventListener('load', () => {
        Utils.log('info', '📄 Document loaded, initializing tracking');
        setTimeout(TrackingInitializer.init, 200);
      }, { once: true });

      window.addEventListener('DOMContentLoaded', () => {
        Utils.log('info', '📄 DOM content loaded, initializing tracking');
        setTimeout(TrackingInitializer.init, 100);
      }, { once: true });
    }
  };

  // Cleanup
  window.addEventListener('beforeunload', () => {
    DOMObserver.observer && DOMObserver.observer.disconnect();
    eventCache.clear();
    Utils.log('info', '🧹 Cleanup completed');
  });

  // Inicialização
  setupEventListeners();

  Utils.log('info', '🎉 GA4 Event Monitor script injection completed');

})(window);
`;

// Export compatível com CommonJS e ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { injectTrackingScript };
} else if (typeof window !== 'undefined') {
  window.injectTrackingScript = injectTrackingScript;
}

export { injectTrackingScript };