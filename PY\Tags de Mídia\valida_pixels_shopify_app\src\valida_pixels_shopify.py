from playwright.sync_api import sync_playwright
import urllib.parse
import streamlit as st

# URLs típicas dos pixels
PIXEL_PATTERNS = {
    'facebook': 'https://www.facebook.com/tr',
    'google_ads': 'https://www.googleadservices.com/pagead/conversion',
}

def parse_query_params(url):
    parsed = urllib.parse.urlparse(url)
    return dict(urllib.parse.parse_qsl(parsed.query))

def run_shopify_test(shop_url):
    results = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        def intercept_request(route, request):
            url = request.url
            for nome, padrao in PIXEL_PATTERNS.items():
                if padrao in url:
                    results.append(f"\n📡 [{nome.upper()} EVENT] Detectado:")
                    results.append(f"➡️ URL: {url}")
                    params = parse_query_params(url)
                    for k, v in params.items():
                        results.append(f"   • {k}: {v}")
            route.continue_()

        page.route("**/*", intercept_request)

        # 1. Acessa a home da loja
        results.append("🔍 Acessando a home...")
        page.goto(shop_url, wait_until="domcontentloaded")
        page.wait_for_timeout(3000)

        # 2. Clica no primeiro produto visível
        results.append("🛍️ Acessando um produto...")
        produto = page.locator("a[href*='/products/']").first
        produto.click()
        page.wait_for_timeout(3000)

        # 3. Clica em “Add to cart” se existir
        results.append("➕ Tentando adicionar ao carrinho...")
        try:
            add_to_cart = page.locator("form[action*='/cart'] [type=submit]").first
            add_to_cart.click()
            page.wait_for_timeout(5000)
        except:
            results.append("⚠️ Botão de adicionar ao carrinho não encontrado.")

        results.append("\n✅ Teste concluído.")
        browser.close()
    
    return results