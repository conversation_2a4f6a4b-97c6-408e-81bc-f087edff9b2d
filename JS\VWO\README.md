# VWO Experiment Data Scraper

This script navigates to https://facamedicina.afya.com.br/, executes a VWO experiment detection script, and extracts the following variables:
- experiment_id
- variation_id
- variation_name

## Prerequisites

- Node.js 14+
- npm or yarn

## Installation

```bash
npm install
```

## Usage

```bash
npm start
```

## How it works

1. The script launches a headless browser using Puppeteer
2. Clears browser cookies and cache
3. Navigates to the target website
4. Injects and executes the VWO experiment detection script
5. Extracts the required variables
6. Displays the results in a formatted output

## Configuration

You can modify the following parameters in the `scraper.js` file:
- EXPERIMENT_ID: The ID of the VWO experiment to look for
- POLL_INTERVAL: How frequently to check for the experiment (in milliseconds)
- MAX_ATTEMPTS: Maximum number of attempts before timing out

## Troubleshooting

If the script fails to extract the data, try:
- Checking your internet connection
- Verifying that the target website is accessible
- Increasing the MAX_ATTEMPTS value in the script
- Running with the `--no-sandbox` flag if on certain Linux environments