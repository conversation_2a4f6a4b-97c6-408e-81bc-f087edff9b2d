#!/usr/bin/env python3
"""
GA4 Analytics Dashboard com IA
===============================

Aplicação Streamlit para análise interativa de dados do Google Analytics 4
com integração de IA para insights automáticos.

Autor: Desenvolvido para análise de dados GA4
Versão: 1.0
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import requests
from pathlib import Path

# Configuração da página
st.set_page_config(
    page_title="GA4 Analytics Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# ============================================================================
# CONFIGURAÇÕES E CONSTANTES
# ============================================================================

# Carregar dimensões e métricas do GA4 dos arquivos JSON
@st.cache_data
def load_ga4_metadata() -> Tuple[Dict, Dict]:
    """
    Carrega as dimensões e métricas disponíveis do GA4 dos arquivos JSON.

    Returns:
        Tuple[Dict, Dict]: Dicionários com dimensões e métricas organizadas por categoria
    """
    try:
        # Carregar dimensões
        with open('ga4_dimensions_json.json', 'r', encoding='utf-8') as f:
            dimensions = json.load(f)

        # Carregar métricas
        with open('ga4_metrics_json.json', 'r', encoding='utf-8') as f:
            metrics = json.load(f)

        return dimensions, metrics
    except FileNotFoundError as e:
        st.error(f"Arquivo não encontrado: {e}")
        return {}, {}
    except json.JSONDecodeError as e:
        st.error(f"Erro ao decodificar JSON: {e}")
        return {}, {}

# Tipos de gráficos disponíveis
CHART_TYPES = {
    "Barras": "bar",
    "Barras Horizontais": "bar_horizontal",
    "Linhas": "line",
    "Área": "area",
    "Pizza": "pie",
    "Dispersão": "scatter",
    "Histograma": "histogram",
    "Box Plot": "box",
    "Heatmap": "heatmap",
    "Funil": "funnel"
}

# ============================================================================
# FUNÇÕES DE INTEGRAÇÃO COM GA4
# ============================================================================

def connect_to_ga4() -> Optional[object]:
    """
    Estabelece conexão com a API do Google Analytics 4.

    Returns:
        Optional[object]: Cliente da API GA4 ou None se falhar
    """
    try:
        from google.analytics.data_v1beta import BetaAnalyticsDataClient

        # Verificar se as credenciais estão configuradas
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        property_id = os.getenv("GA4_PROPERTY_ID")

        if not credentials_path or not property_id:
            st.warning("⚠️ Credenciais do GA4 não configuradas. Usando dados de exemplo.")
            return None

        if not os.path.exists(credentials_path):
            st.error(f"❌ Arquivo de credenciais não encontrado: {credentials_path}")
            return None

        # Criar cliente
        client = BetaAnalyticsDataClient()
        return client

    except ImportError:
        st.error("❌ Biblioteca google-analytics-data não instalada")
        return None
    except Exception as e:
        st.error(f"❌ Erro ao conectar com GA4: {e}")
        return None

def fetch_ga4_data(
    client: Optional[object],
    dimensions: List[str],
    metrics: List[str],
    start_date: str = "7daysAgo",
    end_date: str = "yesterday"
) -> pd.DataFrame:
    """
    Busca dados do Google Analytics 4.

    Args:
        client: Cliente da API GA4
        dimensions: Lista de dimensões selecionadas
        metrics: Lista de métricas selecionadas
        start_date: Data de início
        end_date: Data de fim

    Returns:
        pd.DataFrame: Dados do GA4 ou dados de exemplo
    """
    if client is None:
        # Retornar dados de exemplo se não houver conexão
        return generate_sample_data(dimensions, metrics)

    try:
        from google.analytics.data_v1beta.types import (
            DateRange, Dimension, Metric, RunReportRequest
        )

        property_id = os.getenv("GA4_PROPERTY_ID")

        # Construir request
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name=dim) for dim in dimensions],
            metrics=[Metric(name=metric) for metric in metrics],
            date_ranges=[DateRange(start_date=start_date, end_date=end_date)]
        )

        # Executar request
        response = client.run_report(request)

        # Converter para DataFrame
        data = []
        for row in response.rows:
            row_data = {}

            # Adicionar dimensões
            for i, dim_header in enumerate(response.dimension_headers):
                if i < len(row.dimension_values):
                    row_data[dim_header.name] = row.dimension_values[i].value

            # Adicionar métricas
            for i, metric_header in enumerate(response.metric_headers):
                if i < len(row.metric_values):
                    try:
                        # Tentar converter para número
                        value = float(row.metric_values[i].value)
                    except ValueError:
                        value = row.metric_values[i].value
                    row_data[metric_header.name] = value

            data.append(row_data)

        return pd.DataFrame(data)

    except Exception as e:
        st.error(f"❌ Erro ao buscar dados do GA4: {e}")
        return generate_sample_data(dimensions, metrics)

def generate_sample_data(dimensions: List[str], metrics: List[str]) -> pd.DataFrame:
    """
    Gera dados de exemplo para demonstração.

    Args:
        dimensions: Lista de dimensões
        metrics: Lista de métricas

    Returns:
        pd.DataFrame: Dados de exemplo
    """
    import random
    from datetime import datetime, timedelta

    # Dados de exemplo baseados nas dimensões e métricas selecionadas
    sample_data = []

    # Gerar 30 linhas de dados de exemplo
    for i in range(30):
        row = {}

        # Gerar valores para dimensões
        for dim in dimensions:
            if dim == "date":
                date = datetime.now() - timedelta(days=i)
                row[dim] = date.strftime("%Y%m%d")
            elif dim == "country":
                row[dim] = random.choice(["Brazil", "United States", "United Kingdom", "Germany", "France"])
            elif dim == "deviceCategory":
                row[dim] = random.choice(["desktop", "mobile", "tablet"])
            elif dim == "source":
                row[dim] = random.choice(["google", "facebook", "direct", "twitter", "linkedin"])
            elif dim == "medium":
                row[dim] = random.choice(["organic", "cpc", "social", "email", "referral"])
            else:
                row[dim] = f"Valor_{dim}_{i+1}"

        # Gerar valores para métricas
        for metric in metrics:
            if metric == "totalUsers":
                row[metric] = random.randint(100, 5000)
            elif metric == "newUsers":
                row[metric] = random.randint(50, 2000)
            elif metric == "sessions":
                row[metric] = random.randint(120, 6000)
            elif metric == "bounceRate":
                row[metric] = round(random.uniform(0.2, 0.8), 3)
            elif metric == "averageSessionDuration":
                row[metric] = round(random.uniform(60, 300), 2)
            elif metric == "totalRevenue":
                row[metric] = round(random.uniform(1000, 50000), 2)
            else:
                row[metric] = round(random.uniform(1, 1000), 2)

        sample_data.append(row)

    return pd.DataFrame(sample_data)

# ============================================================================
# FUNÇÕES DE VISUALIZAÇÃO
# ============================================================================

def create_chart(df: pd.DataFrame, chart_type: str, dimensions: List[str], metrics: List[str]) -> go.Figure:
    """
    Cria gráfico baseado no tipo selecionado e dados fornecidos.

    Args:
        df: DataFrame com os dados
        chart_type: Tipo de gráfico
        dimensions: Lista de dimensões
        metrics: Lista de métricas

    Returns:
        go.Figure: Gráfico Plotly
    """
    if df.empty:
        fig = go.Figure()
        fig.add_annotation(text="Nenhum dado disponível",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    # Selecionar primeira dimensão e métrica para o gráfico
    x_col = dimensions[0] if dimensions else df.columns[0]
    y_col = metrics[0] if metrics else df.select_dtypes(include=['number']).columns[0]

    try:
        if chart_type == "bar":
            # Suporte a múltiplas métricas: uma barra para cada métrica
            if len(metrics) > 1:
                fig = px.bar(df, x=x_col, y=metrics, barmode="group", title=f"{' / '.join(metrics)} por {x_col}")
            else:
                fig = px.bar(df, x=x_col, y=y_col, title=f"{y_col} por {x_col}")

        elif chart_type == "bar_horizontal":
            if len(metrics) > 1:
                fig = px.bar(df, y=x_col, x=metrics, orientation='h', barmode="group", title=f"{' / '.join(metrics)} por {x_col}")
            else:
                fig = px.bar(df, x=y_col, y=x_col, orientation='h', title=f"{y_col} por {x_col}")

        elif chart_type == "line":
            # Suporte a múltiplas métricas: uma linha para cada métrica
            if len(metrics) > 1:
                fig = px.line(df, x=x_col, y=metrics, title=f"{' / '.join(metrics)} por {x_col}")
            else:
                fig = px.line(df, x=x_col, y=y_col, title=f"Tendência de {y_col} por {x_col}")

        elif chart_type == "area":
            if len(metrics) > 1:
                fig = px.area(df, x=x_col, y=metrics, title=f"{' / '.join(metrics)} por {x_col}")
            else:
                fig = px.area(df, x=x_col, y=y_col, title=f"Área de {y_col} por {x_col}")

        elif chart_type == "pie":
            # Se múltiplas métricas, mostrar uma pizza para cada
            if len(metrics) > 1:
                from plotly.subplots import make_subplots
                fig = make_subplots(rows=1, cols=len(metrics), specs=[[{"type": "domain"}] * len(metrics)],
                                    subplot_titles=metrics)
                for i, metric in enumerate(metrics):
                    fig.add_trace(
                        px.pie(df, names=x_col, values=metric).data[0],
                        row=1, col=i+1
                    )
                fig.update_layout(title_text=f"Distribuição das métricas por {x_col}")
            else:
                fig = px.pie(df, names=x_col, values=y_col, title=f"Distribuição de {y_col} por {x_col}")

        elif chart_type == "scatter":
            # Precisa de pelo menos 2 métricas
            if len(metrics) > 1:
                fig = px.scatter(df, x=metrics[0], y=metrics[1], color=x_col,
                                 title=f"Dispersão: {metrics[0]} vs {metrics[1]}")
            else:
                fig = go.Figure()
                fig.add_annotation(text="Selecione pelo menos 2 métricas para o gráfico de dispersão.",
                                  xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)

        elif chart_type == "histogram":
            if len(metrics) > 1:
                fig = px.histogram(df, x=metrics, nbins=20, title=f"Distribuição das métricas selecionadas")
            else:
                fig = px.histogram(df, x=y_col, nbins=20, title=f"Distribuição de {y_col}")

        elif chart_type == "box":
            if len(metrics) > 1:
                fig = px.box(df, x=x_col, y=metrics, title=f"Box Plot: {' / '.join(metrics)} por {x_col}")
            else:
                fig = px.box(df, x=x_col, y=y_col, title=f"Box Plot: {y_col} por {x_col}")

        elif chart_type == "heatmap":
            # Criar matriz de correlação para heatmap
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 1:
                corr_matrix = df[numeric_cols].corr()
                fig = px.imshow(corr_matrix, text_auto=True, title="Matriz de Correlação")
            else:
                fig = go.Figure()
                fig.add_annotation(text="Necessário pelo menos 2 métricas numéricas",
                                  xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)

        elif chart_type == "funnel":
            fig = go.Figure(go.Funnel(
                y=df[x_col].head(10),
                x=df[y_col].head(10),
                textinfo="value+percent initial"
            ))
            fig.update_layout(title=f"Funil: {y_col} por {x_col}")

        else:
            # Gráfico padrão (barras)
            fig = px.bar(df, x=x_col, y=metrics if len(metrics) > 1 else y_col)

        # Configurações gerais do gráfico
        fig.update_layout(
            height=500,
            showlegend=True,
            template="plotly_white"
        )

        return fig

    except Exception as e:
        fig = go.Figure()
        fig.add_annotation(text=f"Erro ao criar gráfico: {str(e)}",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

# ============================================================================
# FUNÇÕES DE INTEGRAÇÃO COM IA
# ============================================================================

def call_ai_api(prompt: str, data_summary: str, api_choice: str = "gemini") -> str:
    """
    Chama API de IA para gerar insights sobre os dados.

    Args:
        prompt: Prompt do usuário
        data_summary: Resumo dos dados para contexto
        api_choice: Escolha da API (gemini, openai)

    Returns:
        str: Resposta da IA
    """
    try:
        if api_choice == "gemini":
            return call_gemini_api(prompt, data_summary)
        elif api_choice == "openai":
            return call_openai_api(prompt, data_summary)
        elif api_choice == "local_llm":
            return call_local_llm_api(prompt, data_summary)
        else:
            return "API de IA não configurada. Configure sua chave de API nas configurações."

    except Exception as e:
        return f"Erro ao chamar API de IA: {str(e)}"

def call_gemini_api(prompt: str, data_summary: str) -> str:
    """
    Chama a API do Google Gemini.

    Args:
        prompt: Prompt do usuário
        data_summary: Resumo dos dados

    Returns:
        str: Resposta do Gemini
    """
    api_key = st.session_state.get('gemini_api_key', '')

    if not api_key:
        return "⚠️ Chave da API Gemini não configurada. Configure nas configurações da barra lateral."

    try:
        import google.generativeai as genai

        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')

        # Construir prompt completo
        full_prompt = f"""
        Você é um especialista em análise de dados do Google Analytics 4.

        Contexto dos dados:
        {data_summary}

        Pergunta do usuário:
        {prompt}

        Por favor, forneça insights detalhados, identifique padrões importantes e sugira ações baseadas nos dados.
        Seja específico e prático nas suas recomendações.
        """

        response = model.generate_content(full_prompt)
        return response.text

    except ImportError:
        return "❌ Biblioteca google-generativeai não instalada. Execute: pip install google-generativeai"
    except Exception as e:
        return f"❌ Erro ao chamar Gemini API: {str(e)}"

def call_openai_api(prompt: str, data_summary: str) -> str:
    """
    Chama a API da OpenAI.

    Args:
        prompt: Prompt do usuário
        data_summary: Resumo dos dados

    Returns:
        str: Resposta da OpenAI
    """
    api_key = st.session_state.get('openai_api_key', '')

    if not api_key:
        return "⚠️ Chave da API OpenAI não configurada. Configure nas configurações da barra lateral."

    try:
        import openai

        openai.api_key = api_key

        # Construir prompt completo
        full_prompt = f"""
        Você é um especialista em análise de dados do Google Analytics 4.

        Contexto dos dados:
        {data_summary}

        Pergunta do usuário:
        {prompt}

        Por favor, forneça insights detalhados, identifique padrões importantes e sugira ações baseadas nos dados.
        Seja específico e prático nas suas recomendações.
        """

        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "Você é um especialista em análise de dados do Google Analytics 4."},
                {"role": "user", "content": full_prompt}
            ],
            max_tokens=1000,
            temperature=0.7
        )

        return response.choices[0].message.content

    except ImportError:
        return "❌ Biblioteca openai não instalada. Execute: pip install openai"
    except Exception as e:
        return f"❌ Erro ao chamar OpenAI API: {str(e)}"

def call_local_llm_api(prompt: str, data_summary: str) -> str:
    """
    Chama uma LLM local compatível com OpenAI API (ex: LM Studio, vLLM, Ollama).
    Espera que o endpoint esteja em http://localhost:1234/v1/chat.
    """
    api_url = st.session_state.get('local_llm_url', 'http://localhost:1234/v1/chat')
    api_key = st.session_state.get('local_llm_api_key', None)  # Opcional
    headers = {"Content-Type": "application/json"}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    payload = {
        "model": st.session_state.get('local_llm_model', 'gpt-3.5-turbo'),
        "messages": [
            {"role": "system", "content": "Você é um especialista em análise de dados do Google Analytics 4."},
            {"role": "user", "content": f"Contexto dos dados:\n{data_summary}\n\nPergunta do usuário:\n{prompt}\n\nPor favor, forneça insights detalhados, identifique padrões importantes e sugira ações baseadas nos dados. Seja específico e prático nas suas recomendações."}
        ],
        "max_tokens": 1000,
        "temperature": 0.7
    }
    try:
        response = requests.post(api_url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        result = response.json()
        return result['choices'][0]['message']['content']
    except Exception as e:
        return f"❌ Erro ao chamar LLM local: {str(e)}"

def generate_data_summary(df: pd.DataFrame) -> str:
    """
    Gera um resumo dos dados para enviar à IA.

    Args:
        df: DataFrame com os dados

    Returns:
        str: Resumo dos dados
    """
    if df.empty:
        return "Nenhum dado disponível."

    summary = f"""
    Resumo dos dados:
    - Total de registros: {len(df)}
    - Colunas: {', '.join(df.columns)}
    - Período: {df.index.min() if hasattr(df.index, 'min') else 'N/A'} a {df.index.max() if hasattr(df.index, 'max') else 'N/A'}

    Estatísticas das métricas numéricas:
    {df.describe().to_string() if not df.select_dtypes(include=['number']).empty else 'Nenhuma métrica numérica'}

    Primeiras 5 linhas:
    {df.head().to_string()}
    """

    return summary

def suggest_chart_type(dimensions: List[str], metrics: List[str]) -> str:
    """
    Sugere o melhor tipo de gráfico baseado nas dimensões e métricas selecionadas.

    Args:
        dimensions: Lista de dimensões
        metrics: Lista de métricas

    Returns:
        str: Tipo de gráfico sugerido
    """
    # Lógica simples para sugestão de gráficos
    if not dimensions or not metrics:
        return "bar"

    # Se tem dimensão de tempo, sugerir linha
    time_dimensions = ["date", "dateHour", "month", "year", "week"]
    if any(dim in time_dimensions for dim in dimensions):
        return "line"

    # Se tem muitas categorias, sugerir pizza
    if len(dimensions) == 1 and len(metrics) == 1:
        return "pie"

    # Se tem múltiplas métricas, sugerir dispersão
    if len(metrics) > 1:
        return "scatter"

    # Padrão: barras
    return "bar"

# ============================================================================
# INTERFACE PRINCIPAL
# ============================================================================

def load_env_variables():
    """Carrega variáveis de ambiente do arquivo .env se existir."""
    env_file = Path(".env")

    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if value and not value.startswith('SUBSTITUA'):
                        os.environ[key] = value

def save_env_variables(credentials_path, property_id):
    """Salva as variáveis de ambiente no arquivo .env."""
    env_content = f"""# Configurações do GA4 Analytics Dashboard
# ========================================

# Caminho para o arquivo JSON de credenciais do Google Cloud
GOOGLE_APPLICATION_CREDENTIALS={credentials_path}

# ID da propriedade GA4
GA4_PROPERTY_ID={property_id}

# Configuração automática via interface Streamlit
"""

    with open(".env", 'w', encoding='utf-8') as f:
        f.write(env_content)

def test_ga4_connection_detailed():
    """Testa conexão com GA4 e retorna resultado detalhado."""
    try:
        # Verificar se a biblioteca está instalada
        try:
            from google.analytics.data_v1beta import BetaAnalyticsDataClient
            from google.analytics.data_v1beta.types import (
                DateRange, Dimension, Metric, RunReportRequest
            )
        except ImportError as import_error:
            return False, f"Biblioteca Google Analytics não instalada. Execute: pip install google-analytics-data\nErro: {import_error}"

        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        property_id = os.getenv("GA4_PROPERTY_ID")

        if not credentials_path or not property_id:
            return False, "Credenciais não configuradas"

        if not os.path.exists(credentials_path):
            return False, f"Arquivo de credenciais não encontrado: {credentials_path}"

        # Criar cliente
        client = BetaAnalyticsDataClient()

        # Teste simples
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name="date")],
            metrics=[Metric(name="totalUsers")],
            date_ranges=[DateRange(start_date="7daysAgo", end_date="yesterday")]
        )

        response = client.run_report(request)

        return True, f"Conexão bem-sucedida! {len(response.rows)} registros encontrados."

    except Exception as e:
        error_msg = str(e)

        if "No module named" in error_msg:
            return False, "Biblioteca Google Analytics não instalada. Execute: pip install google-analytics-data"
        elif "SERVICE_DISABLED" in error_msg:
            return False, "Google Analytics Data API não está habilitada. Habilite em: https://console.developers.google.com/apis/api/analyticsdata.googleapis.com/"
        elif "permission" in error_msg.lower() or "forbidden" in error_msg.lower():
            return False, "Service account não tem permissão. Adicione ao GA4 com papel 'Viewer'."
        elif "not found" in error_msg.lower():
            return False, "Property ID não encontrado. Verifique se está correto."
        else:
            return False, f"Erro: {error_msg}"

def show_setup_screen():
    """Exibe tela de configuração/login."""
    st.title("🔧 Configuração do GA4 Analytics Dashboard")
    st.markdown("---")

    # Carregar variáveis existentes
    load_env_variables()

    # Status atual
    current_creds = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", "")
    current_prop_id = os.getenv("GA4_PROPERTY_ID", "")

    # Verificar se já está configurado
    if current_creds and current_prop_id and not current_prop_id.startswith('SUBSTITUA'):
        st.success("✅ Configuração encontrada!")

        col1, col2 = st.columns(2)
        with col1:
            st.info(f"📁 Credenciais: {os.path.basename(current_creds)}")
        with col2:
            st.info(f"🏠 Property ID: {current_prop_id}")

        # Testar conexão
        with st.spinner("🔍 Testando conexão com GA4..."):
            success, message = test_ga4_connection_detailed()

        if success:
            st.success(f"🎉 {message}")

            col1, col2 = st.columns(2)
            with col1:
                if st.button("🚀 Continuar para Dashboard", type="primary", use_container_width=True):
                    st.session_state['ga4_configured'] = True
                    st.rerun()

            with col2:
                if st.button("⚙️ Reconfigurar", use_container_width=True):
                    st.session_state['show_config'] = True
                    st.rerun()

            return True
        else:
            st.error(f"❌ {message}")

            # Se for erro de biblioteca não instalada, mostrar botão de instalação
            if "não instalada" in message or "No module named" in message:
                st.warning("📦 A biblioteca Google Analytics Data não está instalada.")

                if st.button("🔧 Instalar Biblioteca Automaticamente", type="secondary"):
                    with st.spinner("Instalando google-analytics-data..."):
                        try:
                            import subprocess
                            import sys

                            result = subprocess.run([
                                sys.executable, "-m", "pip", "install", "google-analytics-data"
                            ], capture_output=True, text=True)

                            if result.returncode == 0:
                                st.success("✅ Biblioteca instalada com sucesso!")
                                st.info("🔄 Recarregue a página para aplicar as mudanças.")
                                if st.button("🔄 Recarregar Página"):
                                    st.rerun()
                            else:
                                st.error(f"❌ Erro na instalação: {result.stderr}")

                        except Exception as e:
                            st.error(f"❌ Erro ao instalar: {e}")

                st.markdown("**Ou instale manualmente:**")
                st.code("pip install google-analytics-data", language="bash")
            else:
                st.warning("⚠️ Configure as credenciais abaixo para continuar.")

    # Formulário de configuração
    st.subheader("📋 Configuração das Credenciais")

    with st.expander("ℹ️ Como obter as credenciais", expanded=False):
        st.markdown("""
        ### 🔑 Service Account (Arquivo JSON):
        1. Acesse [Google Cloud Console](https://console.cloud.google.com/)
        2. Crie ou selecione um projeto
        3. Habilite "Google Analytics Data API"
        4. Crie Service Account em "APIs & Services" → "Credentials"
        5. Baixe o arquivo JSON

        ### 🏠 Property ID:
        1. Acesse [Google Analytics](https://analytics.google.com/)
        2. Selecione sua propriedade GA4
        3. Clique "Admin" → "Property details"
        4. Copie o Property ID (apenas números)

        ### 👥 Adicionar Service Account ao GA4:
        1. No GA4, vá em "Admin" → "Property access management"
        2. Clique "+" → "Add users"
        3. Adicione o email da service account
        4. Selecione papel "Viewer"
        """)

    # Upload do arquivo JSON
    st.markdown("#### 📁 Upload do Arquivo de Credenciais")
    uploaded_file = st.file_uploader(
        "Selecione o arquivo JSON da service account:",
        type=['json'],
        help="Arquivo JSON baixado do Google Cloud Console"
    )

    credentials_path = ""
    service_account_email = ""

    if uploaded_file is not None:
        try:
            # Salvar arquivo
            credentials_path = f"./{uploaded_file.name}"
            with open(credentials_path, "wb") as f:
                f.write(uploaded_file.getbuffer())

            # Ler e validar JSON
            import json
            with open(credentials_path, 'r') as f:
                cred_data = json.load(f)

            if cred_data.get('type') == 'service_account':
                service_account_email = cred_data.get('client_email', '')
                st.success(f"✅ Arquivo válido! Service Account: {service_account_email}")
            else:
                st.error("❌ Arquivo JSON inválido. Deve ser uma service account.")
                credentials_path = ""

        except Exception as e:
            st.error(f"❌ Erro ao processar arquivo: {e}")
            credentials_path = ""

    # Property ID
    st.markdown("#### 🏠 Property ID do GA4")
    property_id = st.text_input(
        "Digite o Property ID:",
        value=current_prop_id if not current_prop_id.startswith('SUBSTITUA') else "",
        placeholder="*********",
        help="Apenas números. Encontre em GA4 > Admin > Property details"
    )

    # Validação do Property ID
    if property_id:
        if property_id.isdigit() and 8 <= len(property_id) <= 12:
            st.success("✅ Property ID válido")
        else:
            st.error("❌ Property ID deve ter 8-12 dígitos")
            property_id = ""

    # Botão de configuração
    if st.button("💾 Salvar Configuração", type="primary", disabled=not (credentials_path and property_id)):
        try:
            # Salvar variáveis de ambiente
            full_path = os.path.abspath(credentials_path)
            save_env_variables(full_path, property_id)

            # Definir variáveis na sessão atual
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = full_path
            os.environ["GA4_PROPERTY_ID"] = property_id

            st.success("✅ Configuração salva!")

            # Testar conexão
            with st.spinner("🔍 Testando conexão..."):
                success, message = test_ga4_connection_detailed()

            if success:
                st.success(f"🎉 {message}")
                st.balloons()

                if st.button("🚀 Ir para Dashboard"):
                    st.session_state['ga4_configured'] = True
                    st.rerun()
            else:
                st.error(f"❌ {message}")

                if service_account_email:
                    st.warning(f"💡 Adicione este email ao GA4: `{service_account_email}`")

        except Exception as e:
            st.error(f"❌ Erro ao salvar: {e}")

    # Opção de usar dados de exemplo
    st.markdown("---")
    st.markdown("#### 🧪 Ou use dados de exemplo")
    st.info("💡 Você pode explorar a aplicação com dados simulados sem configurar credenciais.")

    if st.button("📊 Continuar com Dados de Exemplo", use_container_width=True):
        st.session_state['ga4_configured'] = True
        st.session_state['use_sample_data'] = True
        st.rerun()

    return False

def main():
    """
    Função principal da aplicação Streamlit.
    """
    # Inicializar estado da sessão
    if 'ga4_configured' not in st.session_state:
        st.session_state['ga4_configured'] = False

    if 'use_sample_data' not in st.session_state:
        st.session_state['use_sample_data'] = False

    # Verificar se precisa mostrar tela de configuração
    if not st.session_state['ga4_configured']:
        if show_setup_screen():
            return
        else:
            return

    # Título principal com versão
    st.title("📊 GA4 Analytics Dashboard com IA  ")
    st.markdown("<span style='font-size: 16px; color: gray;'>Versão: 1.0</span>", unsafe_allow_html=True)
    st.markdown("---")

    # Carregar metadados do GA4
    dimensions_data, metrics_data = load_ga4_metadata()

    if not dimensions_data or not metrics_data:
        st.error("❌ Não foi possível carregar os metadados do GA4. Verifique os arquivos JSON.")
        return

    # Conectar ao GA4
    if st.session_state.get('use_sample_data', False):
        ga4_client = None
    else:
        load_env_variables()
        ga4_client = connect_to_ga4()

    # ========================================================================
    # BARRA LATERAL - CONFIGURAÇÕES
    # ========================================================================

    with st.sidebar:
        st.header("⚙️ Configurações")

        # Status da conexão GA4
        st.subheader("🔗 Status GA4")
        if st.session_state.get('use_sample_data', False):
            st.info("🧪 Usando dados de exemplo")
        elif ga4_client:
            st.success("✅ Conectado ao GA4")
        else:
            st.warning("⚠️ Usando dados de exemplo")

        # Botão de reconfiguração
        if st.button("🔧 Reconfigurar GA4", use_container_width=True):
            st.session_state['ga4_configured'] = False
            st.session_state['use_sample_data'] = False
            st.rerun()

        st.markdown("---")

        # Configurações de API de IA
        st.subheader("🤖 Configuração da IA")

        ai_provider = st.selectbox(
            "Provedor de IA:",
            ["gemini", "openai", "local_llm"],
            help="Escolha o provedor de IA para análises"
        )

        if ai_provider == "gemini":
            gemini_key = st.text_input(
                "Chave API Gemini:",
                type="password",
                value=st.session_state.get('gemini_api_key', ''),
                help="Obtenha em: https://makersuite.google.com/app/apikey"
            )
            if gemini_key:
                st.session_state['gemini_api_key'] = gemini_key

        elif ai_provider == "openai":
            openai_key = st.text_input(
                "Chave API OpenAI:",
                type="password",
                value=st.session_state.get('openai_api_key', ''),
                help="Obtenha em: https://platform.openai.com/api-keys"
            )
            if openai_key:
                st.session_state['openai_api_key'] = openai_key

        elif ai_provider == "local_llm":
            local_llm_url = st.text_input(
                "Endpoint da LLM local:",
                value=st.session_state.get('local_llm_url', 'http://localhost:1234/v1/chat'),
                help="Exemplo: http://localhost:1234/v1/chat (OpenAI compatible)"
            )
            if local_llm_url:
                st.session_state['local_llm_url'] = local_llm_url

            local_llm_model = st.text_input(
                "Nome do modelo local:",
                value=st.session_state.get('local_llm_model', 'gpt-3.5-turbo'),
                help="Exemplo: gpt-3.5-turbo, llama3, etc. (conforme seu servidor local)"
            )
            if local_llm_model:
                st.session_state['local_llm_model'] = local_llm_model

            local_llm_api_key = st.text_input(
                "Chave API (opcional):",
                type="password",
                value=st.session_state.get('local_llm_api_key', ''),
                help="Se seu endpoint exigir autenticação. Deixe em branco se não precisar."
            )
            if local_llm_api_key:
                st.session_state['local_llm_api_key'] = local_llm_api_key

        st.markdown("---")

        # Configurações de período
        st.subheader("📅 Período dos Dados")

        date_range = st.selectbox(
            "Período:",
            ["7daysAgo", "30daysAgo", "90daysAgo", "365daysAgo"],
            help="Período para buscar os dados"
        )

        end_date = st.selectbox(
            "Data final:",
            ["yesterday", "today"],
            help="Data final do período"
        )

        st.markdown("---")

        st.subheader("ℹ️ Sobre")
        st.markdown("GA4 Analytics Dashboard com IA  ")
        st.markdown("<span style='font-size: 14px; color: gray;'>Versão: 1.0</span>", unsafe_allow_html=True)
        st.markdown("Desenvolvido para análise de dados GA4.")
        st.markdown("---")

    # ========================================================================
    # ÁREA PRINCIPAL - SELEÇÃO DE DADOS
    # ========================================================================

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📏 Dimensões")

        # Seletor de categoria de dimensões
        dim_category = st.selectbox(
            "Categoria de Dimensões:",
            list(dimensions_data.keys()),
            help="Selecione uma categoria para filtrar as dimensões"
        )

        # Seletor de dimensões específicas
        available_dimensions = list(dimensions_data[dim_category].keys())
        selected_dimensions = st.multiselect(
            "Dimensões:",
            available_dimensions,
            default=available_dimensions[:2] if len(available_dimensions) >= 2 else available_dimensions,
            help="Selecione as dimensões para análise"
        )

        # Mostrar descrições das dimensões selecionadas
        if selected_dimensions:
            with st.expander("ℹ️ Descrição das Dimensões"):
                for dim in selected_dimensions:
                    st.write(f"**{dim}**: {dimensions_data[dim_category].get(dim, 'Sem descrição')}")

    with col2:
        st.subheader("📊 Métricas")

        # Seletor de categoria de métricas
        metric_category = st.selectbox(
            "Categoria de Métricas:",
            list(metrics_data.keys()),
            help="Selecione uma categoria para filtrar as métricas"
        )

        # Seletor de métricas específicas
        available_metrics = list(metrics_data[metric_category].keys())
        selected_metrics = st.multiselect(
            "Métricas:",
            available_metrics,
            default=available_metrics[:3] if len(available_metrics) >= 3 else available_metrics,
            help="Selecione as métricas para análise"
        )

        # Mostrar descrições das métricas selecionadas
        if selected_metrics:
            with st.expander("ℹ️ Descrição das Métricas"):
                for metric in selected_metrics:
                    st.write(f"**{metric}**: {metrics_data[metric_category].get(metric, 'Sem descrição')}")

    # Validação das seleções
    if not selected_dimensions:
        st.warning("⚠️ Selecione pelo menos uma dimensão.")
        return

    if not selected_metrics:
        st.warning("⚠️ Selecione pelo menos uma métrica.")
        return

    # ========================================================================
    # BUSCAR E PROCESSAR DADOS
    # ========================================================================

    # Botão para buscar dados
    if st.button("🔄 Buscar Dados", type="primary"):
        with st.spinner("Buscando dados do GA4..."):
            # Buscar dados
            df = fetch_ga4_data(
                ga4_client,
                selected_dimensions,
                selected_metrics,
                date_range,
                end_date
            )

            # Armazenar dados na sessão
            st.session_state['current_data'] = df
            st.session_state['current_dimensions'] = selected_dimensions
            st.session_state['current_metrics'] = selected_metrics

    # Verificar se há dados na sessão
    if 'current_data' not in st.session_state:
        st.info("👆 Clique em 'Buscar Dados' para começar a análise.")
        return

    df = st.session_state['current_data']
    current_dimensions = st.session_state['current_dimensions']
    current_metrics = st.session_state['current_metrics']

    if df.empty:
        st.error("❌ Nenhum dado encontrado para os critérios selecionados.")
        return

    # ========================================================================
    # VISUALIZAÇÃO DOS DADOS
    # ========================================================================

    st.markdown("---")
    st.header("📈 Visualização dos Dados")

    # Seletor de tipo de gráfico
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        # Sugerir tipo de gráfico automaticamente
        suggested_chart = suggest_chart_type(current_dimensions, current_metrics)

        chart_type_display = st.selectbox(
            "Tipo de Gráfico:",
            list(CHART_TYPES.keys()),
            index=list(CHART_TYPES.values()).index(suggested_chart) if suggested_chart in CHART_TYPES.values() else 0,
            help="Tipo de gráfico para visualização"
        )

        chart_type = CHART_TYPES[chart_type_display]

    with col2:
        st.metric("Total de Registros", len(df))

    with col3:
        if st.button("🤖 Sugerir Gráfico"):
            suggested = suggest_chart_type(current_dimensions, current_metrics)
            st.info(f"Sugestão: {[k for k, v in CHART_TYPES.items() if v == suggested][0]}")

    # Criar e exibir gráfico
    fig = create_chart(df, chart_type, current_dimensions, current_metrics)
    st.plotly_chart(fig, use_container_width=True)

    # ========================================================================
    # TABELA DE DADOS
    # ========================================================================

    st.subheader("📋 Tabela de Dados")

    # Opções de exibição da tabela
    col1, col2, col3 = st.columns(3)

    with col1:
        show_all = st.checkbox("Mostrar todos os dados", value=False)

    with col2:
        if not show_all:
            num_rows = st.number_input("Número de linhas:", min_value=5, max_value=100, value=20)
        else:
            num_rows = len(df)

    with col3:
        if st.button("📥 Download CSV"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="Baixar dados em CSV",
                data=csv,
                file_name=f"ga4_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

    # Exibir tabela
    if show_all:
        st.dataframe(df, use_container_width=True)
    else:
        st.dataframe(df.head(num_rows), use_container_width=True)

    # Estatísticas básicas
    with st.expander("📊 Estatísticas Descritivas"):
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            st.dataframe(df[numeric_cols].describe(), use_container_width=True)
        else:
            st.info("Nenhuma coluna numérica encontrada para estatísticas.")

    # ========================================================================
    # ANÁLISE COM IA
    # ========================================================================

    st.markdown("---")
    st.header("🤖 Análise com IA")

    # Área para prompt do usuário
    user_prompt = st.text_area(
        "Faça uma pergunta sobre os dados:",
        placeholder="Ex: Quais são os principais insights destes dados? Que padrões você identifica? Que ações você recomenda?",
        height=100,
        help="Digite sua pergunta e a IA analisará os dados para fornecer insights"
    )

    # Botões de prompts pré-definidos
    st.subheader("💡 Prompts Sugeridos")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📈 Análise de Tendências"):
            user_prompt = "Analise as tendências nos dados e identifique padrões temporais importantes."

    with col2:
        if st.button("🎯 Insights de Performance"):
            user_prompt = "Quais são os principais insights de performance e que ações você recomenda?"

    with col3:
        if st.button("🔍 Análise Comparativa"):
            user_prompt = "Compare os diferentes segmentos nos dados e identifique oportunidades."

    # Processar prompt da IA
    if user_prompt and st.button("🚀 Analisar com IA", type="primary"):
        with st.spinner("Analisando dados com IA..."):
            # Gerar resumo dos dados
            data_summary = generate_data_summary(df)

            # Chamar API da IA
            ai_response = call_ai_api(user_prompt, data_summary, ai_provider)

            # Exibir resposta
            st.subheader("🎯 Insights da IA")
            st.markdown(ai_response)

            # Salvar na sessão para histórico
            if 'ai_history' not in st.session_state:
                st.session_state['ai_history'] = []

            st.session_state['ai_history'].append({
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'prompt': user_prompt,
                'response': ai_response
            })

    # Histórico de análises
    if 'ai_history' in st.session_state and st.session_state['ai_history']:
        with st.expander("📚 Histórico de Análises"):
            for i, analysis in enumerate(reversed(st.session_state['ai_history'])):
                st.markdown(f"**{analysis['timestamp']}**")
                st.markdown(f"*Pergunta:* {analysis['prompt']}")
                st.markdown(f"*Resposta:* {analysis['response'][:200]}...")
                st.markdown("---")

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

if __name__ == "__main__":
    main()
