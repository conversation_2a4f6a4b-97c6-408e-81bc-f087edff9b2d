# valida_pixels_shopify_app

This project is designed to validate Shopify pixels for Facebook and Google Ads using Playwright and Streamlit. It provides a simple user interface to interact with the pixel validation functionality.

## Project Structure

```
valida_pixels_shopify_app
├── src
│   ├── valida_pixels_shopify.py  # Contains the main logic for validating Shopify pixels.
│   └── app.py                    # Implements the Streamlit interface for user interaction.
├── requirements.txt               # Lists the necessary libraries for the project.
└── README.md                      # Documentation for the project.
```

## Installation

To set up the project, follow these steps:

1. Clone the repository:

   ```
   git clone <repository-url>
   cd valida_pixels_shopify_app
   ```

2. Install the required libraries:

   ```
   pip install -r requirements.txt
   ```

## Usage

To run the Streamlit application, execute the following command:

```
streamlit run src/app.py
```

This will start the Streamlit server and open the application in your default web browser.

## Functionality

- The application allows users to validate pixel events from Facebook and Google Ads.
- It intercepts network requests and displays detected pixel events along with their query parameters.
- Users can interact with the Shopify store through the provided interface to test pixel validation.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any suggestions or improvements.