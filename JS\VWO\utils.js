const chalk = require('chalk');

/**
 * Log a message with appropriate styling based on the type
 * @param {string} type - The type of log (info, success, warning, error)
 * @param {string} message - The message to log
 */
function log(type, message) {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = `[${timestamp}]`;
  
  switch (type) {
    case 'info':
      console.log(chalk.blue(`${prefix} ℹ️ ${message}`));
      break;
    case 'success':
      console.log(chalk.green(`${prefix} ✅ ${message}`));
      break;
    case 'warning':
      console.log(chalk.yellow(`${prefix} ⚠️ ${message}`));
      break;
    case 'error':
      console.error(chalk.red(`${prefix} ❌ ${message}`));
      break;
    default:
      console.log(`${prefix} ${message}`);
  }
}

/**
 * Log an error message
 * @param {string} message - The error message to log
 */
function logError(message) {
  log('error', message);
}

/**
 * Format and display the VWO experiment results
 * @param {Object} results - The experiment results to format
 */
function formatResults(results) {
  console.log('\n' + chalk.bgBlue.white(' VWO EXPERIMENT RESULTS ') + '\n');
  
  const { experiment_id, variation_id, variation_name } = results;
  
  console.log(chalk.cyan('Experiment ID:   ') + chalk.white(experiment_id));
  console.log(chalk.cyan('Variation ID:    ') + chalk.white(variation_id));
  console.log(chalk.cyan('Variation Name:  ') + chalk.white(variation_name));
  
  console.log('\n' + chalk.green('✅ Data successfully extracted and stored'));
}

module.exports = {
  log,
  logError,
  formatResults
};