import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import * as LucideIcons from 'lucide-react-native';
import Colors from '@/constants/Colors';

interface EmptyStateProps {
  title: string;
  message: string;
  icon: keyof typeof LucideIcons;
}

export default function EmptyState({ title, message, icon }: EmptyStateProps) {
  const IconComponent = LucideIcons[icon] || LucideIcons.AlertCircle;
  
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <IconComponent size={40} color={Colors.light.tint} />
      </View>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.message}>{message}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f2f2f2',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
});