import streamlit as st
from playwright.sync_api import sync_playwright
import urllib.parse
import threading
import queue
import time
from datetime import datetime

# URLs típicas dos pixels
PIXEL_PATTERNS = {
    'facebook': 'https://www.facebook.com/tr',
    'google_ads': 'https://www.googleadservices.com/pagead/conversion',
    'google_analytics': 'https://www.google-analytics.com/collect',
    'google_gtag': 'https://www.googletagmanager.com/gtag/js',
    'tiktok': 'https://analytics.tiktok.com/api/v2/pixel',
    'pinterest': 'https://ct.pinterest.com/v3/',
    'snapchat': 'https://tr.snapchat.com/p',
    'twitter': 'https://t.co/i/adsct',
    'linkedin': 'https://px.ads.linkedin.com/collect'
}

def parse_query_params(url):
    """Extrai parâmetros da query string de uma URL"""
    try:
        parsed = urllib.parse.urlparse(url)
        return dict(urllib.parse.parse_qsl(parsed.query))
    except:
        return {}

def format_event_data(pixel_name, url, params):
    """Formata os dados do evento para exibição"""
    return {
        'timestamp': datetime.now().strftime('%H:%M:%S'),
        'pixel': pixel_name.upper(),
        'url': url,
        'params': params
    }

def run_shopify_test_thread(store_url, selected_pixels, progress_queue, event_queue):
    """Executa o teste em uma thread separada"""
    detected_events = []

    # Filtra apenas os pixels selecionados
    active_patterns = {k: v for k, v in PIXEL_PATTERNS.items() if k in selected_pixels}

    try:
        progress_queue.put("🔧 Iniciando navegador...")
        progress_queue.put(f"🎯 Testando URL: {store_url}")
        progress_queue.put(f"📡 Monitorando {len(selected_pixels)} pixels: {', '.join(selected_pixels)}")

        with sync_playwright() as p:
            progress_queue.put("🌐 Abrindo navegador Chromium...")

            try:
                browser = p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding'
                    ]
                )
                progress_queue.put("✅ Navegador iniciado com sucesso")
            except Exception as e:
                progress_queue.put(f"❌ Erro ao iniciar navegador: {str(e)}")
                return detected_events

            try:
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    viewport={'width': 1920, 'height': 1080},
                    ignore_https_errors=True
                )
                page = context.new_page()
                progress_queue.put("✅ Página criada com sucesso")
            except Exception as e:
                progress_queue.put(f"❌ Erro ao criar página: {str(e)}")
                browser.close()
                return detected_events

            def intercept_request(route, request):
                try:
                    url = request.url
                    # Log de todas as requisições para debug
                    if any(pattern in url for pattern in active_patterns.values()):
                        progress_queue.put(f"🔍 Requisição interceptada: {url[:100]}...")

                    for nome, padrao in active_patterns.items():
                        if padrao in url:
                            params = parse_query_params(url)
                            event_data = format_event_data(nome, url, params)
                            detected_events.append(event_data)
                            event_queue.put(event_data)
                            progress_queue.put(f"🎉 PIXEL DETECTADO: {nome.upper()}")
                    route.continue_()
                except Exception as e:
                    progress_queue.put(f"⚠️ Erro na interceptação: {str(e)}")
                    try:
                        route.continue_()
                    except:
                        pass

            try:
                page.route("**/*", intercept_request)
                progress_queue.put("✅ Interceptação de requisições configurada")
            except Exception as e:
                progress_queue.put(f"❌ Erro ao configurar interceptação: {str(e)}")
                browser.close()
                return detected_events

            progress_queue.put("🔍 Acessando a página inicial da loja...")

            try:
                # Tenta acessar a página
                progress_queue.put(f"🌐 Navegando para: {store_url}")
                response = page.goto(store_url, wait_until="domcontentloaded", timeout=60000)

                if response:
                    status = response.status
                    progress_queue.put(f"✅ Página carregada com status HTTP: {status}")

                    if status >= 400:
                        progress_queue.put(f"⚠️ Status de erro HTTP: {status}")
                        if status == 403:
                            progress_queue.put("🚫 Acesso negado - site pode estar bloqueando bots")
                        elif status == 404:
                            progress_queue.put("🔍 Página não encontrada - verifique a URL")
                        elif status >= 500:
                            progress_queue.put("🔧 Erro do servidor - tente novamente mais tarde")
                else:
                    progress_queue.put("⚠️ Resposta vazia do servidor")

                progress_queue.put("⏳ Aguardando carregamento completo...")
                page.wait_for_timeout(8000)  # Aguarda mais tempo

                # Verifica se a página carregou
                try:
                    title = page.title()
                    url_atual = page.url
                    progress_queue.put(f"📄 Título: {title[:50]}...")
                    progress_queue.put(f"🔗 URL atual: {url_atual}")

                    # Verifica se há conteúdo na página
                    content = page.content()
                    if len(content) < 1000:
                        progress_queue.put("⚠️ Página parece ter pouco conteúdo")
                    else:
                        progress_queue.put(f"✅ Página carregada com {len(content)} caracteres")

                except Exception as e:
                    progress_queue.put(f"⚠️ Erro ao verificar conteúdo da página: {str(e)}")

            except Exception as e:
                progress_queue.put(f"❌ Erro ao carregar página: {str(e)}")
                progress_queue.put("🔄 Tentando continuar mesmo assim...")
                # Não retorna aqui, tenta continuar

            progress_queue.put("🛍️ Procurando produtos na loja...")
            page.wait_for_timeout(2000)
            
            try:
                # Tenta diferentes seletores para produtos
                product_selectors = [
                    "a[href*='/products/']",
                    "a[href*='/product/']", 
                    ".product-item a",
                    ".product a",
                    "[data-product-handle]"
                ]
                
                produto_encontrado = False
                for selector in product_selectors:
                    produtos = page.locator(selector)
                    count = produtos.count()
                    if count > 0:
                        progress_queue.put(f"📦 Encontrados {count} produtos")
                        produtos.first.click()
                        page.wait_for_timeout(5000)
                        produto_encontrado = True
                        progress_queue.put("✅ Produto acessado com sucesso!")
                        break
                
                if not produto_encontrado:
                    progress_queue.put("⚠️ Nenhum produto encontrado - continuando teste na página inicial")
                    
            except Exception as e:
                progress_queue.put(f"⚠️ Erro ao acessar produto: {str(e)}")

            progress_queue.put("🛒 Procurando botão de adicionar ao carrinho...")
            page.wait_for_timeout(2000)
            
            try:
                # Seletores para botões de carrinho
                cart_selectors = [
                    "form[action*='/cart'] [type=submit]",
                    "form[action*='/cart'] button",
                    "[data-testid='add-to-cart']",
                    ".btn-cart",
                    ".add-to-cart",
                    "button[name='add']",
                    ".product-form button[type='submit']",
                    ".shopify-payment-button"
                ]
                
                button_found = False
                for selector in cart_selectors:
                    try:
                        buttons = page.locator(selector)
                        count = buttons.count()
                        if count > 0:
                            progress_queue.put(f"🎯 Encontrado botão: {selector}")
                            buttons.first.click()
                            page.wait_for_timeout(7000)
                            button_found = True
                            progress_queue.put("✅ Produto adicionado ao carrinho!")
                            break
                    except:
                        continue
                
                if not button_found:
                    progress_queue.put("⚠️ Botão de adicionar ao carrinho não encontrado")
                        
            except Exception as e:
                progress_queue.put(f"⚠️ Erro ao adicionar ao carrinho: {str(e)}")

            progress_queue.put("⏳ Aguardando eventos adicionais...")
            page.wait_for_timeout(5000)
            
            progress_queue.put("🔄 Fazendo scroll para ativar mais eventos...")
            try:
                page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                page.wait_for_timeout(3000)
                page.evaluate("window.scrollTo(0, 0)")
                page.wait_for_timeout(2000)
            except:
                pass
            
            progress_queue.put("✅ Teste concluído!")

            try:
                browser.close()
                progress_queue.put("🔒 Navegador fechado")
            except Exception as e:
                progress_queue.put(f"⚠️ Erro ao fechar navegador: {str(e)}")

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        progress_queue.put(f"❌ Erro durante o teste: {str(e)}")
        progress_queue.put(f"🔍 Detalhes do erro: {error_details[:500]}...")

        # Tenta fechar o navegador mesmo com erro
        try:
            if 'browser' in locals():
                browser.close()
        except:
            pass

    progress_queue.put(f"📊 Total de eventos detectados: {len(detected_events)}")
    return detected_events

def main():
    st.set_page_config(
        page_title="Validador de Pixels Shopify",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 Validador de Pixels Shopify")
    st.markdown("**Ferramenta para detectar e validar pixels de rastreamento em lojas Shopify**")
    
    # Sidebar com configurações
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        # URL da loja
        store_url = st.text_input(
            "🏪 URL da Loja Shopify",
            placeholder="https://minhaloja.myshopify.com",
            help="Digite a URL completa da loja Shopify que deseja testar"
        )
        
        # Seleção de pixels
        st.subheader("📡 Pixels para Monitorar")
        selected_pixels = []
        
        for pixel_name, pixel_url in PIXEL_PATTERNS.items():
            if st.checkbox(pixel_name.replace('_', ' ').title(), value=True):
                selected_pixels.append(pixel_name)
        
        # Botão para iniciar teste
        start_test = st.button("🚀 Iniciar Teste", type="primary", use_container_width=True)
    
    # Área principal
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.subheader("📊 Status do Teste")
        status_container = st.empty()
    
    with col2:
        st.subheader("📡 Eventos Detectados")
        events_container = st.empty()
    
    # Inicializa session state
    if 'events' not in st.session_state:
        st.session_state.events = []
    if 'test_running' not in st.session_state:
        st.session_state.test_running = False
    
    # Executa o teste
    if start_test and store_url and selected_pixels and not st.session_state.test_running:
        if not store_url.startswith(('http://', 'https://')):
            st.error("❌ Por favor, insira uma URL válida (deve começar com http:// ou https://)")
        else:
            st.session_state.test_running = True
            st.session_state.events = []
            
            # Cria filas para comunicação com a thread
            progress_queue = queue.Queue()
            event_queue = queue.Queue()
            
            # Inicia o teste em uma thread separada
            test_thread = threading.Thread(
                target=run_shopify_test_thread,
                args=(store_url, selected_pixels, progress_queue, event_queue)
            )
            test_thread.start()
            
            # Monitora o progresso
            status_placeholder = status_container.empty()
            events_placeholder = events_container.empty()
            
            # Loop para atualizar a interface
            progress_bar = st.progress(0)
            max_wait_time = 120  # 2 minutos máximo
            start_time = time.time()

            while test_thread.is_alive():
                current_time = time.time()
                elapsed_time = current_time - start_time

                # Atualiza barra de progresso
                progress_percentage = min(elapsed_time / max_wait_time, 1.0)
                progress_bar.progress(progress_percentage)

                # Atualiza status
                try:
                    while not progress_queue.empty():
                        status = progress_queue.get_nowait()
                        status_placeholder.info(status)
                        st.rerun()  # Força atualização da interface
                except queue.Empty:
                    pass

                # Atualiza eventos
                try:
                    while not event_queue.empty():
                        event_data = event_queue.get_nowait()
                        st.session_state.events.append(event_data)

                        # Atualiza a exibição de eventos
                        if st.session_state.events:
                            events_df = []
                            for event in st.session_state.events:
                                events_df.append({
                                    'Horário': event['timestamp'],
                                    'Pixel': event['pixel'],
                                    'Parâmetros': len(event['params'])
                                })
                            events_placeholder.dataframe(events_df, use_container_width=True)
                        st.rerun()  # Força atualização da interface
                except queue.Empty:
                    pass

                # Timeout de segurança
                if elapsed_time > max_wait_time:
                    status_placeholder.warning("⏰ Teste interrompido por timeout - mas pode ter detectado pixels")
                    break

                time.sleep(1)  # Pausa de 1 segundo

            # Processa mensagens finais
            final_messages = []
            try:
                while not progress_queue.empty():
                    final_messages.append(progress_queue.get_nowait())
            except queue.Empty:
                pass

            try:
                while not event_queue.empty():
                    event_data = event_queue.get_nowait()
                    st.session_state.events.append(event_data)
            except queue.Empty:
                pass

            progress_bar.empty()  # Remove a barra de progresso
            
            # Aguarda a thread terminar
            test_thread.join()
            st.session_state.test_running = False
            
            # Exibe resultados finais
            if st.session_state.events:
                status_placeholder.success(f"✅ Teste concluído! {len(st.session_state.events)} eventos detectados.")
                
                # Exibe detalhes dos eventos
                st.subheader("📋 Detalhes dos Eventos")
                
                for i, event in enumerate(st.session_state.events):
                    with st.expander(f"🔍 {event['pixel']} - {event['timestamp']}"):
                        st.write(f"**URL:** {event['url']}")
                        if event['params']:
                            st.write("**Parâmetros:**")
                            for key, value in event['params'].items():
                                st.write(f"• **{key}:** {value}")
                        else:
                            st.write("*Nenhum parâmetro detectado*")
            else:
                status_placeholder.warning("⚠️ Teste concluído, mas nenhum pixel foi detectado.")
    
    elif start_test and not store_url:
        st.error("❌ Por favor, insira a URL da loja Shopify")
    elif start_test and not selected_pixels:
        st.error("❌ Por favor, selecione pelo menos um pixel para monitorar")
    elif start_test and st.session_state.test_running:
        st.warning("⏳ Um teste já está em execução. Aguarde a conclusão.")
    
    # Informações adicionais
    with st.expander("ℹ️ Como usar esta ferramenta"):
        st.markdown("""
        ### 📖 Instruções de Uso
        
        1. **Configure a URL**: Digite a URL completa da loja Shopify que deseja testar
        2. **Selecione os Pixels**: Marque quais pixels de rastreamento você quer monitorar
        3. **Execute o Teste**: Clique em "Iniciar Teste" para começar a análise
        
        ### 🔍 O que a ferramenta faz
        
        - Acessa a página inicial da loja
        - Navega para um produto (se disponível)
        - Tenta adicionar o produto ao carrinho
        - Monitora todas as requisições de rede em busca dos pixels selecionados
        - Captura e exibe os parâmetros enviados para cada pixel
        
        ### 📡 Pixels Suportados
        
        - **Facebook Pixel**: Rastreamento de conversões do Facebook/Meta
        - **Google Ads**: Conversões do Google Ads
        - **Google Analytics**: Eventos do Google Analytics
        - **Google Gtag**: Google Tag Manager
        - **TikTok Pixel**: Rastreamento do TikTok Ads
        - **Pinterest**: Conversões do Pinterest
        - **Snapchat**: Pixel do Snapchat Ads
        - **Twitter**: Rastreamento do Twitter Ads
        - **LinkedIn**: Conversões do LinkedIn Ads
        """)

if __name__ == "__main__":
    main()
