const puppeteer = require('puppeteer');
const { log, logError } = require('./utils');

/**
 * Extracts VWO experiment data from a given URL
 * @param {string} url - The target URL to scrape
 * @returns {Promise<Object|null>} - The extracted experiment data or null if not found
 */
async function extractVwoData(url) {
  let browser = null;
  
  try {
    log('info', 'Launching browser...');
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Clear cookies and cache before navigation
    log('info', 'Clearing cookies and cache...');
    const client = await page.target().createCDPSession();
    await client.send('Network.clearBrowserCookies');
    await client.send('Network.clearBrowserCache');
    
    // Set a reasonable timeout
    page.setDefaultNavigationTimeout(30000);
    
    log('info', `Navigating to ${url}...`);
    await page.goto(url, { waitUntil: 'networkidle2' });
    
    log('info', 'Injecting and executing VWO experiment detection script...');
    
    // The script to inject - a modified version of the provided script
    // that returns the experiment data instead of just logging it
    const vwoScript = `
      (function pollVWOExperiment() {
        return new Promise((resolve) => {
          var EXPERIMENT_ID = 29;
          var POLL_INTERVAL = 200;
          var MAX_ATTEMPTS = 30;
          var attempts = 0;
          
          function check() {
            var exp = window._vwo_exp && window._vwo_exp[EXPERIMENT_ID];
            if (exp && exp.combination_chosen !== undefined) {
              var variationId = exp.combination_chosen;
              var variationName;
              
              // Get variation name from comb_n if available
              if (exp.comb_n && exp.comb_n[variationId]) {
                variationName = exp.comb_n[variationId];
              } else {
                // Fallback to default names
                var variations = {
                  1: "Control",
                  2: "Variante"
                };
                variationName = variations[variationId] || ("Variation " + variationId);
              }
              
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                event: "vwo_experiment_viewed",
                experiment_id: EXPERIMENT_ID,
                variation_id: variationId,
                variation_name: variationName
              });
              
              resolve({
                experiment_id: EXPERIMENT_ID,
                variation_id: variationId,
                variation_name: variationName
              });
              return;
            }
            
            if (attempts < MAX_ATTEMPTS) {
              attempts++;
              setTimeout(check, POLL_INTERVAL);
            } else {
              resolve(null);
              console.log('VWO: Max attempts reached, experiment not found');
            }
          }
          check();
        });
      })();
    `;
    
    // Execute the script and wait for the result
    const result = await page.evaluate(vwoScript);
    
    if (result) {
      log('success', 'VWO experiment data extracted successfully!');
      return result;
    } else {
      log('warning', 'VWO experiment not found or timed out');
      return null;
    }
  } catch (error) {
    logError(`Failed to extract VWO data: ${error.message}`);
    return null;
  } finally {
    if (browser) {
      log('info', 'Closing browser...');
      await browser.close();
    }
  }
}

module.exports = {
  extractVwoData
};