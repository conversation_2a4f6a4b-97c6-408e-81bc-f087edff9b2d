<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VWO Scraper Control</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f4f4f4; margin: 0; padding: 0; }
    .container { max-width: 400px; margin: 60px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 32px; }
    h2 { text-align: center; color: #2c3e50; }
    .switch { display: flex; align-items: center; justify-content: center; margin: 32px 0; }
    .switch input { display: none; }
    .slider { width: 60px; height: 34px; background: #ccc; border-radius: 34px; position: relative; transition: background 0.3s; cursor: pointer; }
    .slider:before { content: ""; position: absolute; left: 4px; top: 4px; width: 26px; height: 26px; background: #fff; border-radius: 50%; transition: 0.3s; }
    input:checked + .slider { background: #27ae60; }
    input:checked + .slider:before { transform: translateX(26px); }
    .status { text-align: center; font-size: 1.1em; margin-top: 16px; }
    button { display: block; margin: 24px auto 0; padding: 10px 24px; background: #2980b9; color: #fff; border: none; border-radius: 4px; font-size: 1em; cursor: pointer; }
    button:disabled { background: #aaa; }
  </style>
</head>
<body>
  <div class="container">
    <h2>VWO Scraper</h2>
    <div class="switch">
      <label>
        <input type="checkbox" id="toggleScript">
        <span class="slider"></span>
      </label>
    </div>
    <div class="status" id="status">Script desligado</div>
    <button id="runBtn" disabled>Executar agora</button>
  </div>
  <script>
    const toggle = document.getElementById('toggleScript');
    const status = document.getElementById('status');
    const runBtn = document.getElementById('runBtn');
    let scriptOn = false;

    toggle.addEventListener('change', () => {
      scriptOn = toggle.checked;
      status.textContent = scriptOn ? 'Script ligado' : 'Script desligado';
      runBtn.disabled = !scriptOn;
      // Aqui você pode fazer uma requisição para backend para ligar/desligar
    });

    runBtn.addEventListener('click', async () => {
      status.textContent = 'Executando...';
      runBtn.disabled = true;
      try {
        const res = await fetch('/run-scraper', { method: 'POST' });
        const data = await res.json();
        if (data.success) {
          status.textContent = '✅ Dados extraídos!';
        } else {
          status.textContent = '⚠️ Nenhum experimento encontrado.';
        }
      } catch (e) {
        status.textContent = '❌ Erro ao executar o script.';
      }
      runBtn.disabled = !scriptOn;
    });
  </script>
</body>
</html>
