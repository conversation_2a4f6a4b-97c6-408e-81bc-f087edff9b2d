import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Event {
  id: string;
  type: 'dataLayerEvent' | 'ga4Event' | 'ga4NetworkEvent'; // Adiciona suporte ao novo tipo
  data: any;
  url: string;
  timestamp: string;
}

interface EventState {
  events: Event[];
  addEvent: (event: Event) => void;
  removeEvent: (id: string) => void;
  clearEvents: () => void;
}

export const useEventStore = create<EventState>()(
  persist(
    (set) => ({
      events: [],
      addEvent: (event) => set((state) => ({ 
        events: [...state.events, event] 
      })),
      removeEvent: (id) => set((state) => ({ 
        events: state.events.filter(event => event.id !== id) 
      })),
      clearEvents: () => set({ events: [] }),
    }),
    {
      name: 'event-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);