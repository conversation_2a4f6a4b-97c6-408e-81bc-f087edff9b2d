# valida_pixels_shopify_app

This project is designed to validate Shopify pixels using <PERSON><PERSON> and provides a simple user interface with Streamlit.

## Project Structure

```
valida_pixels_shopify_app
├── src
│   ├── valida_pixels_shopify.py  # Main logic for validating Shopify pixels
│   └── app.py                    # Streamlit application interface
├── requirements.txt               # Required Python libraries
└── README.md                      # Project documentation
```

## Installation

To set up the project, follow these steps:

1. Clone the repository:
   ```
   git clone <repository-url>
   cd valida_pixels_shopify_app
   ```

2. Create a virtual environment (optional but recommended):
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows use `venv\Scripts\activate`
   ```

3. Install the required libraries:
   ```
   pip install -r requirements.txt
   ```

## Usage

To run the Streamlit application, execute the following command in your terminal:

```
streamlit run src/app.py
```

This will start the Streamlit server and open the application in your default web browser.

## Features

- Validate Facebook and Google Ads pixels on Shopify.
- Intercept network requests to detect pixel events.
- User-friendly interface for running tests.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.