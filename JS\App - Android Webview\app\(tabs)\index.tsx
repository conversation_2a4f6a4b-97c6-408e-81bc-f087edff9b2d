import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, TextInput, View, TouchableOpacity, Text, ActivityIndicator, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import * as WebBrowser from 'expo-web-browser';
import { X, ArrowLeft, ArrowRight, RefreshCw as Refresh, Search } from 'lucide-react-native';
import { useEventStore } from '@/store/eventStore';
import { injectTrackingScript } from '@/utils/webViewScripts';
import { useHistoryStore } from '@/store/historyStore';
import { StatusBar } from 'expo-status-bar';
import Colors from '@/constants/Colors';
import { SafeAreaView } from 'react-native-safe-area-context';
import Constants from 'expo-constants';

export default function BrowserScreen() {
  const [url, setUrl] = useState('');
  const [currentUrl, setCurrentUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const webViewRef = useRef(null);
  const addEvent = useEventStore((state) => state.addEvent);
  const addHistoryItem = useHistoryStore((state) => state.addHistoryItem);

  const handleSubmit = () => {
    let processedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      processedUrl = `https://${url}`;
    }
    setCurrentUrl(processedUrl);
    addHistoryItem({
      url: processedUrl,
      title: processedUrl,
      timestamp: new Date().toISOString(),
    });
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
  };

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (
        data.type === 'dataLayerEvent' ||
        data.type === 'ga4Event' ||
        data.type === 'ga4NetworkEvent' // <- Adiciona suporte ao novo tipo
      ) {
        addEvent({
          id: Date.now().toString(),
          type: data.type,
          data: data.payload,
          url: currentUrl,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  const clearUrl = () => {
    setUrl('');
  };

  const goBack = () => {
    webViewRef.current?.goBack();
  };

  const goForward = () => {
    webViewRef.current?.goForward();
  };

  const reload = () => {
    webViewRef.current?.reload();
  };

  const openInBrowser = async () => {
    if (currentUrl) {
      await WebBrowser.openBrowserAsync(currentUrl);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['right', 'left']}>
      <StatusBar style="auto" />
      <View style={styles.urlBar}>
        <View style={styles.inputContainer}>
          <Search size={18} color="#999" style={styles.searchIcon} />
          <TextInput
            style={styles.urlInput}
            value={url}
            onChangeText={setUrl}
            placeholder="Enter URL"
            autoCapitalize="none"
            autoCorrect={false}
            keyboardType="url"
            returnKeyType="go"
            onSubmitEditing={handleSubmit}
          />
          {url.length > 0 && (
            <TouchableOpacity onPress={clearUrl} style={styles.clearButton}>
              <X size={18} color="#999" />
            </TouchableOpacity>
          )}
        </View>
        <TouchableOpacity onPress={handleSubmit} style={styles.goButton}>
          <Text style={styles.goButtonText}>Go</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.browserContent}>
        {currentUrl ? (
          <WebView
            ref={webViewRef}
            source={{ uri: currentUrl }}
            style={styles.webView}
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            onMessage={handleMessage}
            injectedJavaScriptBeforeContentLoaded={injectTrackingScript}
            javaScriptEnabled={true}
            domStorageEnabled={true}
          />
        ) : (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>Enter a URL to start browsing</Text>
          </View>
        )}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.light.tint} />
          </View>
        )}
      </View>

      <View style={styles.toolbar}>
        <TouchableOpacity style={styles.toolbarButton} onPress={goBack}>
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.toolbarButton} onPress={goForward}>
          <ArrowRight size={24} color="#333" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.toolbarButton} onPress={reload}>
          <Refresh size={24} color="#333" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.toolbarButton} onPress={openInBrowser}>
          <Text style={styles.openText}>Open in Browser</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  urlBar: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f2f2f2',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 6,
  },
  urlInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  clearButton: {
    padding: 6,
  },
  goButton: {
    marginLeft: 10,
    backgroundColor: Colors.light.tint,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
  },
  goButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  browserContent: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingVertical: 10,
  },
  toolbarButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  openText: {
    fontSize: 14,
    color: Colors.light.tint,
    fontWeight: '600',
  },
});