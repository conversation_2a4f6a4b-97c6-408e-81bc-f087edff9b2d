<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Extrator e Interpretador de Comentários do YouTube</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f7f7f7; }
        .container { background: #fff; padding: 24px; border-radius: 8px; max-width: 600px; margin: auto; box-shadow: 0 2px 8px #0001; }
        input, button, textarea { width: 100%; margin: 8px 0; padding: 8px; border-radius: 4px; border: 1px solid #ccc; }
        button { background: #1976d2; color: #fff; border: none; cursor: pointer; }
        button:hover { background: #1565c0; }
        .comment { background: #f1f1f1; margin: 8px 0; padding: 8px; border-radius: 4px; }
        .interpretation { color: #1976d2; margin-top: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Extrator e Interpretador de Comentários do YouTube</h2>
        <label for="videoUrl">URL do vídeo do YouTube:</label>
        <input type="text" id="videoUrl" placeholder="Cole a URL completa do vídeo">
        <button onclick="buscarComentarios()">Buscar Comentários</button>
        <div id="comentarios"></div>
    </div>
    <script src="app.js"></script>
</body>
</html>
