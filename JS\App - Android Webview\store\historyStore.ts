import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface HistoryItem {
  url: string;
  title: string;
  timestamp: string;
}

interface HistoryState {
  historyItems: HistoryItem[];
  addHistoryItem: (item: HistoryItem) => void;
  removeHistoryItem: (timestamp: string) => void;
  clearHistory: () => void;
}

export const useHistoryStore = create<HistoryState>()(
  persist(
    (set) => ({
      historyItems: [],
      addHistoryItem: (item) => set((state) => {
        // Don't add duplicate URLs in quick succession
        const isDuplicate = state.historyItems.some(
          existingItem => existingItem.url === item.url && 
          new Date(item.timestamp).getTime() - new Date(existingItem.timestamp).getTime() < 60000
        );
        
        if (isDuplicate) {
          return { historyItems: state.historyItems };
        }
        
        return { historyItems: [...state.historyItems, item] };
      }),
      removeHistoryItem: (timestamp) => set((state) => ({ 
        historyItems: state.historyItems.filter(item => item.timestamp !== timestamp) 
      })),
      clearHistory: () => set({ historyItems: [] }),
    }),
    {
      name: 'history-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);