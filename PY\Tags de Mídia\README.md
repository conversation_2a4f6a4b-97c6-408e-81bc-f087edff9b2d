# 🔍 Validador de Pixels Shopify

Uma ferramenta web interativa para detectar e validar pixels de rastreamento em lojas Shopify.

## 📋 Funcionalidades

- **Interface Web Intuitiva**: Interface criada com Streamlit para facilidade de uso
- **Múltiplos Pixels**: Suporte para Facebook, Google Ads, Google Analytics, TikTok, Pinterest, Snapchat, Twitter e LinkedIn
- **Teste Automatizado**: Navega automaticamente pela loja, acessa produtos e tenta adicionar ao carrinho
- **Monitoramento em Tempo Real**: Exibe eventos detectados conforme eles acontecem
- **Detalhes Completos**: Mostra URLs e parâmetros de cada pixel detectado

## 🚀 Como Usar

### Instalação Rápida

1. **Execute o arquivo de instalação**:
   ```bash
   executar_validador.bat
   ```

### Instalação Manual

1. **Instale as dependências**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Instale os navegadores do Playwright**:
   ```bash
   playwright install
   ```

3. **Execute a aplicação**:
   ```bash
   streamlit run validador_pixels_simples.py
   ```

4. **Acesse no navegador**:
   ```
   http://localhost:8501
   ```

## 🎯 Como Usar a Interface

1. **Configure a URL**: Digite a URL completa da loja Shopify que deseja testar
2. **Selecione os Pixels**: Marque quais pixels de rastreamento você quer monitorar
3. **Execute o Teste**: Clique em "Iniciar Teste" para começar a análise
4. **Acompanhe o Progresso**: Veja o status do teste em tempo real
5. **Analise os Resultados**: Examine os eventos detectados e seus parâmetros

## 📡 Pixels Suportados

- **Facebook Pixel**: Rastreamento de conversões do Facebook/Meta
- **Google Ads**: Conversões do Google Ads
- **Google Analytics**: Eventos do Google Analytics
- **Google Gtag**: Google Tag Manager
- **TikTok Pixel**: Rastreamento do TikTok Ads
- **Pinterest**: Conversões do Pinterest
- **Snapchat**: Pixel do Snapchat Ads
- **Twitter**: Rastreamento do Twitter Ads
- **LinkedIn**: Conversões do LinkedIn Ads

## 🔧 Requisitos

- Python 3.8+
- Streamlit 1.44.0+
- Playwright 1.52.0+

## 📁 Estrutura dos Arquivos

```
PY/Tags de Mídia/
├── validador_pixels_simples.py    # Aplicação principal (versão corrigida)
├── validador_pixels_streamlit.py  # Versão anterior (com problemas de asyncio)
├── valida_pixels_shopify.py       # Script original (linha de comando)
├── requirements.txt               # Dependências
├── executar_validador.bat         # Script de instalação/execução
└── README.md                      # Este arquivo
```

## 💡 Dicas de Uso

- Use URLs de lojas reais para obter resultados mais precisos
- Alguns pixels podem demorar alguns segundos para disparar
- Se nenhum pixel for detectado, verifique se a loja realmente possui os pixels instalados
- A ferramenta funciona melhor com lojas que possuem produtos disponíveis

## 🐛 Solução de Problemas

### Erro "NotImplementedError"
Se você encontrar erros relacionados ao asyncio, certifique-se de que:
- Está usando Python 3.8-3.12 (Python 3.13 pode ter problemas de compatibilidade)
- O Playwright está instalado corretamente: `playwright install`

### Nenhum Pixel Detectado
- Verifique se a URL da loja está correta
- Confirme se a loja possui os pixels instalados
- Tente com uma loja diferente para testar a ferramenta

### Problemas de Conexão
- Verifique sua conexão com a internet
- Algumas lojas podem ter proteções contra automação

## 📞 Suporte

Para problemas ou sugestões, verifique:
1. Se todas as dependências estão instaladas corretamente
2. Se a URL da loja está acessível
3. Se os pixels estão realmente instalados na loja

---

**Desenvolvido para facilitar a validação de pixels de rastreamento em lojas Shopify** 🛍️
