from playwright.sync_api import sync_playwright
import urllib.parse

# URLs típicas dos pixels
PIXEL_PATTERNS = {
    'facebook': 'https://www.facebook.com/tr',
    'google_ads': 'https://www.googleadservices.com/pagead/conversion',
}

def parse_query_params(url):
    parsed = urllib.parse.urlparse(url)
    return dict(urllib.parse.parse_qsl(parsed.query))

def close_popups(page):
    """Tenta fechar pop-ups comuns em sites de e-commerce"""
    popup_selectors = [
        # Seletores comuns para botões de fechar pop-ups
        '[data-dismiss="modal"]',
        '.modal-close',
        '.popup-close',
        '.close-popup',
        '.modal .close',
        '.overlay .close',
        '[aria-label="Close"]',
        '[aria-label="Fechar"]',
        '.newsletter-popup .close',
        '.age-verification .close',
        '.cookie-banner .close',
        '.promo-popup .close',
        # Botões com texto comum
        'button:has-text("×")',
        'button:has-text("Fechar")',
        'button:has-text("Close")',
        'button:has-text("No thanks")',
        'button:has-text("Não, obrigado")',
        'button:has-text("Skip")',
        'button:has-text("Pular")',
        # Overlays clicáveis
        '.modal-backdrop',
        '.overlay',
        '.popup-overlay'
    ]

    for selector in popup_selectors:
        try:
            elements = page.locator(selector)
            if elements.count() > 0:
                print(f"🚫 Fechando pop-up: {selector}")
                elements.first.click()
                page.wait_for_timeout(1000)
                return True
        except:
            continue
    return False

def run_shopify_test():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()

        def intercept_request(route, request):
            url = request.url
            for nome, padrao in PIXEL_PATTERNS.items():
                if padrao in url:
                    print(f"\n📡 [{nome.upper()} EVENT] Detectado:")
                    print(f"➡️ URL: {url}")
                    params = parse_query_params(url)
                    for k, v in params.items():
                        print(f"   • {k}: {v}")
            route.continue_()

        page.route("**/*", intercept_request)

        # 1. Acessa a home da loja
        print("🔍 Acessando a home...")
        page.goto("https://zerezes.com.br", wait_until="domcontentloaded")
        page.wait_for_timeout(3000)

        # 2. Tenta fechar pop-ups
        print("🚫 Verificando e fechando pop-ups...")
        popup_closed = close_popups(page)
        if popup_closed:
            print("✅ Pop-up fechado com sucesso!")
            page.wait_for_timeout(2000)
        else:
            print("ℹ️ Nenhum pop-up detectado ou já fechado")

        # Tenta fechar pop-ups novamente após um tempo (alguns aparecem com delay)
        page.wait_for_timeout(2000)
        close_popups(page)

        # 2. Clica no primeiro produto visível
        print("🛍️ Acessando um produto...")
        produto = page.locator("a[href*='/products/']").first
        produto.click()
        page.wait_for_timeout(3000)

        # 3. Clica em “Add to cart” se existir
        print("➕ Tentando adicionar ao carrinho...")
        try:
            add_to_cart = page.locator("form[action*='/cart'] [type=submit]").first
            add_to_cart.click()
            page.wait_for_timeout(5000)
        except:
            print("⚠️ Botão de adicionar ao carrinho não encontrado.")

        print("\n✅ Teste concluído.")
        browser.close()

if __name__ == "__main__":
    run_shopify_test()
