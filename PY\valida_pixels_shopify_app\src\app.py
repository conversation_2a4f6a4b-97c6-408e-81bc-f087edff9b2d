from streamlit import st
from valida_pixels_shopify import run_shopify_test

def main():
    st.title("Shopify Pixel Validator")
    st.write("This application validates Facebook and Google Ads pixels on a Shopify store.")

    if st.button("Run Pixel Validation"):
        st.write("🔍 Running pixel validation...")
        run_shopify_test()
        st.write("✅ Test completed.")

if __name__ == "__main__":
    main()