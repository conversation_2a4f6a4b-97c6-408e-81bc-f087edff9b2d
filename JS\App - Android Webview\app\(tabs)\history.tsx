import React, { useState } from 'react';
import { StyleSheet, Text, View, FlatList, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useHistoryStore } from '@/store/historyStore';
import { Clock, Link, Trash2, ExternalLink } from 'lucide-react-native';
import { formatDate } from '@/utils/dateFormatter';
import Colors from '@/constants/Colors';
import { useRouter } from 'expo-router';
import EmptyState from '@/components/EmptyState';

export default function HistoryScreen() {
  const { historyItems, clearHistory, removeHistoryItem } = useHistoryStore();
  const router = useRouter();

  const sortedHistory = [...historyItems].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  const handleNavigateToUrl = (url) => {
    // Navigate to browser tab with the selected URL
    router.navigate({
      pathname: '/(tabs)/',
      params: { url }
    });
  };

  const renderItem = ({ item }) => (
    <View style={styles.historyItem}>
      <TouchableOpacity 
        style={styles.historyContent}
        onPress={() => handleNavigateToUrl(item.url)}
      >
        <View style={styles.iconContainer}>
          <Clock size={20} color="#999" />
        </View>
        <View style={styles.historyDetails}>
          <Text style={styles.historyUrl} numberOfLines={1}>{item.url}</Text>
          <Text style={styles.historyTimestamp}>{formatDate(item.timestamp)}</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity 
        style={styles.deleteButton}
        onPress={() => removeHistoryItem(item.timestamp)}
      >
        <Trash2 size={18} color="#FF453A" />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['right', 'left']}>
      <View style={styles.header}>
        <Text style={styles.title}>Browsing History</Text>
        {historyItems.length > 0 && (
          <TouchableOpacity style={styles.clearButton} onPress={clearHistory}>
            <Text style={styles.clearButtonText}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>

      {historyItems.length === 0 ? (
        <EmptyState 
          title="No browsing history"
          message="Your browsing history will appear here"
          icon="Clock"
        />
      ) : (
        <FlatList
          data={sortedHistory}
          renderItem={renderItem}
          keyExtractor={item => item.timestamp}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  clearButton: {
    padding: 8,
  },
  clearButtonText: {
    color: Colors.light.tint,
    fontWeight: '500',
  },
  listContainer: {
    padding: 16,
  },
  historyItem: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  historyContent: {
    flex: 1,
    flexDirection: 'row',
    padding: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f2f2f2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  historyUrl: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 4,
  },
  historyTimestamp: {
    fontSize: 13,
    color: '#999',
  },
  deleteButton: {
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});