const express = require('express');
const cors = require('cors');
const path = require('path');
const scraper = require('./scraper');

const app = express();
app.use(cors());
app.use(express.static(__dirname));
app.use(express.json());

// Endpoint para rodar o scraper
app.post('/run-scraper', async (req, res) => {
  try {
    const url = 'https://facamedicina.afya.com.br/'; // Você pode trocar por req.body.url se quiser customizar
    const result = await scraper.extractVwoData(url);
    res.json({ success: !!result, data: result });
  } catch (e) {
    res.status(500).json({ success: false, error: e.message });
  }
});

// Servir a interface
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'control.html'));
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`VWO Scraper Control rodando em http://localhost:${PORT}`);
});
