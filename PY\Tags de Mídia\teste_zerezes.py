#!/usr/bin/env python3
"""
Teste específico para o site zerezes.com.br
"""

from playwright.sync_api import sync_playwright
import traceback

def test_zerezes():
    print("🔧 Testando acesso ao zerezes.com.br...")
    
    try:
        with sync_playwright() as p:
            print("✅ Playwright iniciado")
            
            browser = p.chromium.launch(
                headless=False,  # Visível para debug
                args=[
                    '--no-sandbox', 
                    '--disable-dev-shm-usage', 
                    '--disable-web-security'
                ]
            )
            print("✅ Navegador aberto")
            
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                ignore_https_errors=True
            )
            page = context.new_page()
            print("✅ Página criada")
            
            # Intercepta requisições para ver o que está acontecendo
            def intercept_request(route, request):
                url = request.url
                print(f"📡 Requisição: {url}")
                
                # Verifica pixels conhecidos
                pixels = {
                    'facebook': 'facebook.com/tr',
                    'google': 'google-analytics.com',
                    'gtag': 'googletagmanager.com'
                }
                
                for nome, pattern in pixels.items():
                    if pattern in url:
                        print(f"🎉 PIXEL DETECTADO: {nome} - {url}")
                
                route.continue_()
            
            page.route("**/*", intercept_request)
            print("✅ Interceptação configurada")
            
            # Tenta acessar o site
            print("🌐 Acessando zerezes.com.br...")
            try:
                response = page.goto("https://zerezes.com.br", wait_until="domcontentloaded", timeout=30000)
                print(f"✅ Resposta recebida: {response.status}")
                
                # Aguarda carregamento
                page.wait_for_timeout(5000)
                
                # Verifica informações da página
                title = page.title()
                url = page.url
                print(f"📄 Título: {title}")
                print(f"🔗 URL: {url}")
                
                # Verifica se há produtos
                print("🛍️ Procurando produtos...")
                produtos = page.locator("a[href*='produto'], a[href*='product'], .product a, .produto a")
                count = produtos.count()
                print(f"📦 Produtos encontrados: {count}")
                
                if count > 0:
                    print("🎯 Clicando no primeiro produto...")
                    produtos.first.click()
                    page.wait_for_timeout(5000)
                    
                    new_title = page.title()
                    new_url = page.url
                    print(f"📄 Nova página - Título: {new_title}")
                    print(f"🔗 Nova URL: {new_url}")
                
                # Aguarda mais um pouco para capturar eventos
                print("⏳ Aguardando eventos...")
                page.wait_for_timeout(10000)
                
            except Exception as e:
                print(f"❌ Erro ao acessar: {str(e)}")
                print(f"🔍 Detalhes: {traceback.format_exc()}")
            
            print("🔒 Fechando navegador...")
            browser.close()
            
    except Exception as e:
        print(f"❌ Erro geral: {str(e)}")
        print(f"🔍 Detalhes: {traceback.format_exc()}")

if __name__ == "__main__":
    test_zerezes()
