"""
Componentes de Layout para GA4 Analytics Dashboard com Dash
===========================================================

Este módulo contém os componentes de layout e interface do usuário
para o dashboard GA4 usando Dash.
"""

import dash
from dash import html, dcc
import dash_bootstrap_components as dbc
from typing import Dict, List, Optional


def create_header() -> html.Div:
    """
    Cria o cabeçalho da aplicação.
    
    Returns:
        html.Div: Componente do cabeçalho
    """
    return html.Div([
        dbc.Row([
            dbc.Col([
                html.H1("📊 GA4 Analytics Dashboard com IA", 
                       className="text-primary mb-0"),
                html.P("Análise interativa de dados do Google Analytics 4 com integração de IA",
                      className="text-muted")
            ], width=8),
            dbc.Col([
                dbc.Badge("Conectado", color="success", id="connection-status"),
            ], width=4, className="text-end")
        ]),
        html.Hr()
    ], className="mb-4")


def create_sidebar() -> dbc.Col:
    """
    Cria a barra lateral com configurações.
    
    Returns:
        dbc.Col: Componente da barra lateral
    """
    return dbc.Col([
        html.H4("⚙️ Configurações", className="mb-3"),
        
        # Status da conexão GA4
        dbc.Card([
            dbc.CardBody([
                html.H6("🔗 Status GA4", className="card-title"),
                html.P(id="ga4-status", className="card-text"),
                dbc.Button("🔧 Reconfigurar GA4", 
                          id="btn-reconfig", 
                          color="outline-secondary", 
                          size="sm",
                          className="w-100")
            ])
        ], className="mb-3"),
        
        # Configurações de IA
        dbc.Card([
            dbc.CardBody([
                html.H6("🤖 Configuração da IA", className="card-title"),
                dbc.Label("Provedor de IA:"),
                dcc.Dropdown(
                    id="ai-provider",
                    options=[
                        {"label": "Google Gemini", "value": "gemini"},
                        {"label": "OpenAI", "value": "openai"}
                    ],
                    value="gemini",
                    className="mb-2"
                ),
                dbc.Label("Chave API:"),
                dbc.Input(
                    id="api-key",
                    type="password",
                    placeholder="Digite sua chave API",
                    className="mb-2"
                ),
                dbc.FormText("Configure sua chave API para usar a IA")
            ])
        ], className="mb-3"),
        
        # Configurações de período
        dbc.Card([
            dbc.CardBody([
                html.H6("📅 Período dos Dados", className="card-title"),
                dbc.Label("Período:"),
                dcc.Dropdown(
                    id="date-range",
                    options=[
                        {"label": "Últimos 7 dias", "value": "7daysAgo"},
                        {"label": "Últimos 30 dias", "value": "30daysAgo"},
                        {"label": "Últimos 90 dias", "value": "90daysAgo"},
                        {"label": "Último ano", "value": "365daysAgo"}
                    ],
                    value="7daysAgo",
                    className="mb-2"
                ),
                dbc.Label("Data final:"),
                dcc.Dropdown(
                    id="end-date",
                    options=[
                        {"label": "Ontem", "value": "yesterday"},
                        {"label": "Hoje", "value": "today"}
                    ],
                    value="yesterday"
                )
            ])
        ])
        
    ], width=3, className="bg-light p-3")


def create_data_selection() -> html.Div:
    """
    Cria a seção de seleção de dados.
    
    Returns:
        html.Div: Componente de seleção de dados
    """
    return html.Div([
        dbc.Row([
            dbc.Col([
                html.H5("📏 Dimensões", className="mb-3"),
                dbc.Label("Categoria de Dimensões:"),
                dcc.Dropdown(
                    id="dimension-category",
                    placeholder="Selecione uma categoria",
                    className="mb-2"
                ),
                dbc.Label("Dimensões:"),
                dcc.Dropdown(
                    id="selected-dimensions",
                    multi=True,
                    placeholder="Selecione as dimensões",
                    className="mb-2"
                ),
                dbc.Collapse([
                    dbc.Card([
                        dbc.CardBody([
                            html.H6("ℹ️ Descrição das Dimensões"),
                            html.Div(id="dimension-descriptions")
                        ])
                    ])
                ], id="dimension-info-collapse", is_open=False),
                dbc.Button("ℹ️ Ver Descrições", 
                          id="btn-dimension-info", 
                          color="outline-info", 
                          size="sm",
                          className="mt-2")
            ], width=6),
            
            dbc.Col([
                html.H5("📊 Métricas", className="mb-3"),
                dbc.Label("Categoria de Métricas:"),
                dcc.Dropdown(
                    id="metric-category",
                    placeholder="Selecione uma categoria",
                    className="mb-2"
                ),
                dbc.Label("Métricas:"),
                dcc.Dropdown(
                    id="selected-metrics",
                    multi=True,
                    placeholder="Selecione as métricas",
                    className="mb-2"
                ),
                dbc.Collapse([
                    dbc.Card([
                        dbc.CardBody([
                            html.H6("ℹ️ Descrição das Métricas"),
                            html.Div(id="metric-descriptions")
                        ])
                    ])
                ], id="metric-info-collapse", is_open=False),
                dbc.Button("ℹ️ Ver Descrições", 
                          id="btn-metric-info", 
                          color="outline-info", 
                          size="sm",
                          className="mt-2")
            ], width=6)
        ]),
        
        html.Hr(),
        
        dbc.Row([
            dbc.Col([
                dbc.Button("🔄 Buscar Dados", 
                          id="btn-fetch-data", 
                          color="primary", 
                          size="lg",
                          className="w-100",
                          disabled=True)
            ], width=12)
        ])
    ], className="mb-4")


def create_visualization_section() -> html.Div:
    """
    Cria a seção de visualização.
    
    Returns:
        html.Div: Componente de visualização
    """
    return html.Div([
        html.H4("📈 Visualização dos Dados", className="mb-3"),
        
        dbc.Row([
            dbc.Col([
                dbc.Label("Tipo de Gráfico:"),
                dcc.Dropdown(
                    id="chart-type",
                    options=[
                        {"label": "Barras", "value": "bar"},
                        {"label": "Barras Horizontais", "value": "bar_horizontal"},
                        {"label": "Linhas", "value": "line"},
                        {"label": "Área", "value": "area"},
                        {"label": "Pizza", "value": "pie"},
                        {"label": "Dispersão", "value": "scatter"},
                        {"label": "Histograma", "value": "histogram"},
                        {"label": "Box Plot", "value": "box"},
                        {"label": "Heatmap", "value": "heatmap"},
                        {"label": "Funil", "value": "funnel"}
                    ],
                    value="bar"
                )
            ], width=4),
            dbc.Col([
                html.Div(id="data-metrics", className="text-center")
            ], width=4),
            dbc.Col([
                dbc.Button("🤖 Sugerir Gráfico", 
                          id="btn-suggest-chart", 
                          color="outline-info",
                          className="w-100")
            ], width=4)
        ], className="mb-3"),
        
        # Área do gráfico
        dcc.Loading([
            dcc.Graph(id="main-chart", style={"height": "500px"})
        ], type="default"),
        
    ], id="visualization-section", style={"display": "none"})


def create_data_table_section() -> html.Div:
    """
    Cria a seção da tabela de dados.
    
    Returns:
        html.Div: Componente da tabela de dados
    """
    return html.Div([
        html.H5("📋 Tabela de Dados", className="mb-3"),
        
        dbc.Row([
            dbc.Col([
                dbc.Checklist(
                    id="show-all-data",
                    options=[{"label": "Mostrar todos os dados", "value": "all"}],
                    value=[]
                )
            ], width=4),
            dbc.Col([
                dbc.Label("Número de linhas:"),
                dbc.Input(
                    id="num-rows",
                    type="number",
                    min=5,
                    max=100,
                    value=20,
                    size="sm"
                )
            ], width=4),
            dbc.Col([
                dbc.Button("📥 Download CSV", 
                          id="btn-download", 
                          color="success",
                          className="w-100")
            ], width=4)
        ], className="mb-3"),
        
        # Tabela de dados
        html.Div(id="data-table"),
        
        # Estatísticas descritivas
        dbc.Collapse([
            dbc.Card([
                dbc.CardBody([
                    html.H6("📊 Estatísticas Descritivas"),
                    html.Div(id="descriptive-stats")
                ])
            ])
        ], id="stats-collapse", is_open=False),
        
        dbc.Button("📊 Ver Estatísticas", 
                  id="btn-stats", 
                  color="outline-secondary", 
                  size="sm",
                  className="mt-2")
        
    ], id="data-table-section", style={"display": "none"})


def create_ai_analysis_section() -> html.Div:
    """
    Cria a seção de análise com IA.
    
    Returns:
        html.Div: Componente de análise com IA
    """
    return html.Div([
        html.H4("🤖 Análise com IA", className="mb-3"),
        
        dbc.Row([
            dbc.Col([
                dbc.Label("Faça uma pergunta sobre os dados:"),
                dbc.Textarea(
                    id="user-prompt",
                    placeholder="Ex: Quais são os principais insights destes dados? Que padrões você identifica? Que ações você recomenda?",
                    rows=4,
                    className="mb-2"
                ),
                dbc.FormText("Digite sua pergunta e a IA analisará os dados para fornecer insights")
            ], width=12)
        ]),
        
        html.H6("💡 Prompts Sugeridos", className="mt-3 mb-2"),
        dbc.Row([
            dbc.Col([
                dbc.Button("📈 Análise de Tendências", 
                          id="btn-trends", 
                          color="outline-primary",
                          className="w-100 mb-2")
            ], width=4),
            dbc.Col([
                dbc.Button("🎯 Insights de Performance", 
                          id="btn-performance", 
                          color="outline-primary",
                          className="w-100 mb-2")
            ], width=4),
            dbc.Col([
                dbc.Button("🔍 Análise Comparativa", 
                          id="btn-comparative", 
                          color="outline-primary",
                          className="w-100 mb-2")
            ], width=4)
        ]),
        
        dbc.Row([
            dbc.Col([
                dbc.Button("🚀 Analisar com IA", 
                          id="btn-analyze", 
                          color="primary", 
                          size="lg",
                          className="w-100 mt-3",
                          disabled=True)
            ], width=12)
        ]),
        
        # Área de resposta da IA
        html.Div(id="ai-response-area", className="mt-4"),
        
        # Histórico de análises
        dbc.Collapse([
            dbc.Card([
                dbc.CardBody([
                    html.H6("📚 Histórico de Análises"),
                    html.Div(id="ai-history")
                ])
            ])
        ], id="history-collapse", is_open=False),
        
        dbc.Button("📚 Ver Histórico", 
                  id="btn-history", 
                  color="outline-secondary", 
                  size="sm",
                  className="mt-2")
        
    ], id="ai-analysis-section", style={"display": "none"})


def create_setup_modal() -> dbc.Modal:
    """
    Cria o modal de configuração inicial.
    
    Returns:
        dbc.Modal: Modal de configuração
    """
    return dbc.Modal([
        dbc.ModalHeader([
            html.H4("🔧 Configuração do GA4 Analytics Dashboard")
        ]),
        dbc.ModalBody([
            html.Div(id="setup-content")
        ]),
        dbc.ModalFooter([
            dbc.Button("📊 Continuar com Dados de Exemplo", 
                      id="btn-sample-data", 
                      color="secondary"),
            dbc.Button("💾 Salvar Configuração", 
                      id="btn-save-config", 
                      color="primary")
        ])
    ], id="setup-modal", is_open=True, backdrop="static")
