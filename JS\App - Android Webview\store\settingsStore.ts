import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SettingsState {
  darkMode: boolean;
  userAgent: string;
  autoCapture: boolean;
  captureDataLayer: boolean;
  captureGA4: boolean;
  updateDarkMode: (value: boolean) => void;
  updateUserAgent: (value: string) => void;
  updateAutoCapture: (value: boolean) => void;
  updateCaptureDataLayer: (value: boolean) => void;
  updateCaptureGA4: (value: boolean) => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      darkMode: false,
      userAgent: '',
      autoCapture: true,
      captureDataLayer: true,
      captureGA4: true,
      updateDarkMode: (value) => set({ darkMode: value }),
      updateUserAgent: (value) => set({ userAgent: value }),
      updateAutoCapture: (value) => set({ autoCapture: value }),
      updateCaptureDataLayer: (value) => set({ captureDataLayer: value }),
      updateCaptureGA4: (value) => set({ captureGA4: value }),
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);