const express = require('express');
const {BetaAnalyticsDataClient} = require('@google-analytics/data');
const bodyParser = require('body-parser');
const app = express();
const port = 3000;

// Substitua pelo caminho para seu arquivo JSON:
const client = new BetaAnalyticsDataClient({
  keyFilename: './SEU-ARQUIVO-JSON-GOOGLE.json'
});

app.use(bodyParser.json());

app.post('/ga4', async (req, res) => {
  try {
    // Pegue os parâmetros do body ou defina fixos para teste
    const propertyId = req.body.propertyId || 'properties/SEU_PROPERTY_ID';
    const startDate = req.body.startDate || '2024-06-01';
    const endDate = req.body.endDate || '2024-06-28';
    const metric = req.body.metric || 'sessions';

    const [report] = await client.runReport({
      property: propertyId,
      dateRanges: [{ startDate, endDate }],
      metrics: [{ name: metric }]
    });

    // Exemplo simples: pega só o valor da primeira linha
    const value = report.rows[0]?.metricValues[0]?.value || '0';
    res.json({ message: `Entre ${startDate} e ${endDate} houve ${value} ${metric}.` });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Erro ao consultar GA4' });
  }
});

app.listen(port, () => {
  console.log(`Servidor rodando em http://localhost:${port}`);
});
