import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useEventStore } from '@/store/eventStore';
import { Filter, Search, Download, Trash2 } from 'lucide-react-native';
import { formatDate } from '@/utils/dateFormatter';
import Colors from '@/constants/Colors';
import EventDetailsModal from '@/components/EventDetailsModal';
import EmptyState from '@/components/EmptyState';

export default function EventsScreen() {
  const { events, clearEvents } = useEventStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const filteredEvents = events.filter(event => {
    const matchesSearch = searchQuery === '' || 
      JSON.stringify(event.data).toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.url.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
      (filterType === 'dataLayer' && event.type === 'dataLayerEvent') ||
      (filterType === 'ga4' && (event.type === 'ga4Event' || event.type === 'ga4NetworkEvent'));
    
    return matchesSearch && matchesFilter;
  }).sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  const handleViewEvent = (event) => {
    setSelectedEvent(event);
    setIsModalVisible(true);
  };

  const exportEvents = () => {
    // In a real app, this would handle exporting the events
    alert('Export functionality would save events to a file');
  };

  const renderItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.eventCard}
      onPress={() => handleViewEvent(item)}
    >
      <View style={styles.eventHeader}>
        <View style={[
          styles.eventTypeBadge, 
          item.type === 'dataLayerEvent' ? styles.dataLayerBadge : styles.ga4Badge
        ]}>
          <Text style={styles.eventTypeBadgeText}>
            {item.type === 'dataLayerEvent' ? 'Data Layer' : 'GA4'}
          </Text>
        </View>
        <Text style={styles.eventTimestamp}>{formatDate(item.timestamp)}</Text>
      </View>
      
      <Text style={styles.eventUrl} numberOfLines={1}>{item.url}</Text>
      
      <View style={styles.eventDataPreview}>
        <Text style={styles.previewText} numberOfLines={2}>
          {JSON.stringify(item.data).substring(0, 100)}
          {JSON.stringify(item.data).length > 100 ? '...' : ''}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['right', 'left']}>
      <View style={styles.header}>
        <Text style={styles.title}>Event Logs</Text>
        <View style={styles.actions}>
          <TouchableOpacity style={styles.actionButton} onPress={exportEvents}>
            <Download size={20} color={Colors.light.tint} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={clearEvents}>
            <Trash2 size={20} color="#FF453A" />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={18} color="#999" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search events..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, filterType === 'all' && styles.activeFilter]}
          onPress={() => setFilterType('all')}
        >
          <Text style={[styles.filterText, filterType === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, filterType === 'dataLayer' && styles.activeFilter]}
          onPress={() => setFilterType('dataLayer')}
        >
          <Text style={[styles.filterText, filterType === 'dataLayer' && styles.activeFilterText]}>Data Layer</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, filterType === 'ga4' && styles.activeFilter]}
          onPress={() => setFilterType('ga4')}
        >
          <Text style={[styles.filterText, filterType === 'ga4' && styles.activeFilterText]}>GA4</Text>
        </TouchableOpacity>
      </View>

      {events.length === 0 ? (
        <EmptyState 
          title="No events captured yet"
          message="Browse a website to start capturing Data Layer and GA4 events"
          icon="BarChart3"
        />
      ) : filteredEvents.length === 0 ? (
        <EmptyState 
          title="No matching events"
          message="Try changing your search or filter settings"
          icon="Filter"
        />
      ) : (
        <FlatList
          data={filteredEvents}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}

      <EventDetailsModal
        isVisible={isModalVisible}
        event={selectedEvent}
        onClose={() => setIsModalVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 10,
    borderWidth: 1,
    borderColor: '#eee',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#f2f2f2',
  },
  activeFilter: {
    backgroundColor: Colors.light.tint,
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  eventCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  eventTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  dataLayerBadge: {
    backgroundColor: '#0A84FF20',
  },
  ga4Badge: {
    backgroundColor: '#FF950020',
  },
  eventTypeBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#0A84FF',
  },
  eventTimestamp: {
    fontSize: 12,
    color: '#999',
  },
  eventUrl: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  eventDataPreview: {
    backgroundColor: '#f9f9f9',
    padding: 8,
    borderRadius: 4,
  },
  previewText: {
    fontSize: 13,
    fontFamily: 'monospace',
    color: '#333',
  },
});