#!/usr/bin/env python3
"""
Script de teste para verificar se o Playwright está funcionando corretamente
"""

import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    print("🔧 Testando Playwright...")
    
    try:
        async with async_playwright() as p:
            print("✅ Playwright iniciado com sucesso")
            
            browser = await p.chromium.launch(headless=True)
            print("✅ Navegador Chromium aberto")
            
            context = await browser.new_context()
            page = await context.new_page()
            print("✅ Nova página criada")
            
            # Testa acesso a uma página simples
            print("🌐 Testando acesso ao Google...")
            await page.goto("https://www.google.com", timeout=30000)
            
            title = await page.title()
            print(f"✅ Página carregada: {title}")
            
            await browser.close()
            print("✅ Navegador fechado")
            
            print("\n🎉 Playwright está funcionando corretamente!")
            
    except Exception as e:
        print(f"❌ Erro no teste do Playwright: {str(e)}")
        import traceback
        print(f"Detalhes: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_playwright())
